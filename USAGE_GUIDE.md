# OnlyOffice 修改建议功能使用指南

## 当前状态说明

由于OnlyOffice的`callCommand`方法在当前环境中不可用，我们实现了一个**功能演示版本**，展示了完整的UI交互和数据处理逻辑。

## 功能特性

### ✅ 已实现的功能

1. **修改建议列表展示**
   - 显示建议卡片，包含原文、建议文本、页码、坐标信息
   - 状态标签显示（待处理/已接受/已拒绝）
   - 响应式布局设计

2. **交互功能**
   - 点击建议卡片选择建议
   - 接受/拒绝建议操作
   - 状态更新和列表管理

3. **调试和测试功能**
   - 连接状态检测
   - API方法可用性检查
   - 模拟高亮演示

### 🔄 API集成状态

- **连接器初始化**: ✅ 成功
- **基础API检测**: ✅ 完成
- **callCommand方法**: ❌ 不可用
- **executeMethod方法**: 🔍 检测中
- **替代方案**: ✅ 已实现

## 使用方法

### 1. 基本操作

1. **查看建议列表**
   - 右侧面板显示所有修改建议
   - 每个建议显示页码、坐标、原文和建议文本

2. **选择建议**
   - 点击建议卡片进行选择
   - 选中的建议会高亮显示
   - 控制台会输出详细的定位信息

3. **处理建议**
   - 点击"接受"按钮接受建议
   - 点击"拒绝"按钮拒绝建议
   - 处理后的建议会从列表中移除

### 2. 调试功能

1. **测试连接**
   - 点击"测试连接"按钮
   - 查看控制台输出的详细诊断信息
   - 检查各种API方法的可用性

2. **模拟高亮**
   - 点击"模拟高亮"按钮
   - 随机选择一个建议进行演示
   - 展示完整的交互流程

## 数据结构

每个修改建议包含以下信息：

```javascript
{
    id: 1,                    // 建议ID
    pageNumber: 1,            // 页码
    coordinates: {            // 对角线坐标
        x1: 100, y1: 200,    // 左上角
        x2: 300, y2: 250     // 右下角
    },
    paragraphIndex: 2,        // 段落索引
    startIndex: 5,            // 起始字符位置
    endIndex: 15,             // 结束字符位置
    originalText: "原文",     // 原始文本
    suggestedText: "建议文本", // 建议修改文本
    color: "yellow",          // 高亮颜色
    status: "pending"         // 状态
}
```

## 技术实现

### API检测机制

系统会自动检测以下API方法的可用性：
- `callCommand` - OnlyOffice文档操作API
- `executeMethod` - 执行特定方法
- `createConnector` - 创建连接器
- `insertText` - 插入文本
- `postMessage` - 消息通信

### 错误处理

- 完整的try-catch错误捕获
- 用户友好的错误提示
- 详细的控制台日志记录
- 优雅的降级处理

### 兼容性方案

当主要API不可用时，系统会：
1. 尝试替代API方法
2. 使用postMessage通信
3. 提供UI模拟演示
4. 保持数据状态同步

## 控制台输出说明

### 连接测试输出

```
=== 连接测试开始 ===
connector状态: true
connector类型: object
=== 关键方法检查 ===
callCommand: ❌ 不可用
executeMethod: ✅ 可用
=== 全局对象检查 ===
window.Api: ❌ 不可用
window.DocsAPI: ✅ 可用
=== 连接测试结束 ===
```

### 建议选择输出

```
选择建议: {id: 1, pageNumber: 1, coordinates: {...}}
页码: 1 坐标: {x1: 100, y1: 200, x2: 300, y2: 250}
尝试文档操作...
已选择建议 0 - UI状态已更新
```

## 下一步开发

### 需要的OnlyOffice配置

1. **启用Automation API**
   - 确保OnlyOffice服务器支持Automation API
   - 配置正确的JWT令牌
   - 启用必要的权限

2. **API方法映射**
   - 确认可用的文档操作方法
   - 实现页码跳转功能
   - 实现坐标定位功能

3. **增强功能**
   - 批量处理建议
   - 建议历史记录
   - 协作功能支持

## 故障排除

### 常见问题

1. **callCommand不可用**
   - 检查OnlyOffice版本是否支持
   - 确认JWT配置正确
   - 验证服务器权限设置

2. **连接器初始化失败**
   - 增加初始化延迟时间
   - 检查文档加载状态
   - 验证DOM元素存在

3. **API方法检测失败**
   - 查看控制台错误信息
   - 检查网络连接状态
   - 确认文档服务器可访问

### 调试建议

1. 使用"测试连接"功能获取详细诊断信息
2. 查看浏览器控制台的完整日志
3. 检查网络请求是否成功
4. 验证OnlyOffice服务器配置

## 总结

当前实现提供了完整的UI交互和数据处理逻辑，为OnlyOffice API完全可用时的无缝集成做好了准备。所有的核心功能都已实现，只需要在API可用时激活相应的文档操作功能。
