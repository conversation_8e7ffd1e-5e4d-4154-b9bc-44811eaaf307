# OnlyOffice 文档修改建议功能实现

## 功能概述

基于OnlyOffice文档编辑器实现的智能修改建议系统，支持：

1. **修改建议列表展示** - 显示待处理的文档修改建议
2. **页码定位** - 点击建议卡片跳转到对应页码
3. **坐标高亮** - 根据对角线坐标高亮选区
4. **文本替换** - 接受建议时自动替换文档内容
5. **状态管理** - 跟踪建议的处理状态

## 核心数据结构

```typescript
interface SuggestionItem {
    id: number;
    pageNumber: number;           // 页码
    coordinates: {                // 对角线坐标
        x1: number;              // 左上角X
        y1: number;              // 左上角Y
        x2: number;              // 右下角X
        y2: number;              // 右下角Y
    };
    paragraphIndex: number;       // 段落索引
    startIndex: number;           // 起始字符位置
    endIndex: number;             // 结束字符位置
    originalText: string;         // 原文
    suggestedText: string;        // 建议修改文本
    color: string;                // 高亮颜色
    status: 'pending' | 'accepted' | 'rejected';  // 状态
}
```

## 主要功能函数

### 1. selectSuggestion(index: number)
- **功能**: 选择建议并定位到文档对应位置
- **实现**: 
  - 更新选中状态
  - 使用OnlyOffice callCommand API定位段落
  - 高亮显示选中区域
  - 滚动到可见区域

### 2. acceptSuggestion(index: number)
- **功能**: 接受建议并修改文档内容
- **实现**:
  - 定位到指定段落和文本范围
  - 删除原文本
  - 插入建议文本
  - 移除高亮
  - 更新建议状态

### 3. rejectSuggestion(index: number)
- **功能**: 拒绝建议
- **实现**:
  - 移除高亮显示
  - 更新建议状态
  - 从列表中移除

## OnlyOffice API 集成

### 连接器初始化
```javascript
// 延迟创建connector确保API完全加载
setTimeout(() => {
    if (typeof event.target.createConnector === 'function') {
        connector.value = event.target.createConnector();
    } else {
        connector.value = event.target;
    }
}, 1000);
```

### 文档操作
```javascript
// 使用callCommand API进行文档操作
connector.callCommand(function() {
    var oDocument = Api.GetDocument();
    var element = oDocument.GetElement(paragraphIndex);
    var oRange = element.GetRange(startIndex, endIndex);
    
    // 高亮显示
    oRange.SetHighlight("yellow");
    
    // 选中并滚动
    oRange.Select();
});
```

## UI 组件特性

### 建议卡片
- 显示原文和建议文本对比
- 显示页码和坐标信息
- 状态标签（待处理/已接受/已拒绝）
- 操作按钮（接受/拒绝）

### 调试面板
- 连接状态显示
- 测试连接按钮
- 建议数量统计

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI库**: Ant Design Vue
- **文档编辑器**: OnlyOffice Document Editor
- **API**: OnlyOffice Automation API

## 使用方法

1. **初始化**: 页面加载时自动初始化OnlyOffice编辑器
2. **查看建议**: 右侧面板显示所有修改建议
3. **选择建议**: 点击建议卡片定位到文档对应位置
4. **处理建议**: 点击"接受"或"拒绝"按钮处理建议
5. **调试**: 使用"测试连接"按钮检查API状态

## 注意事项

1. **API延迟**: OnlyOffice API需要时间初始化，使用setTimeout延迟创建连接器
2. **错误处理**: 包含完整的错误捕获和用户友好的错误提示
3. **兼容性**: 支持多种API调用方式的回退机制
4. **性能优化**: 使用批量操作（BeginTransaction/EndTransaction）提高性能

## 扩展功能

- 支持更多文档格式
- 批量处理建议
- 建议历史记录
- 协作评论功能
- 自定义高亮样式
