# OnlyOffice 页码跳转功能测试指南

## 功能说明

已实现的页码跳转和高亮功能，支持：
1. **点击建议卡片** - 自动跳转到对应页码并高亮选区
2. **测试跳转按钮** - 快速测试页码跳转功能
3. **多重API支持** - 自动检测并使用最佳的API方法

## 测试步骤

### 1. 基础测试
1. 打开页面，等待OnlyOffice编辑器加载完成
2. 查看右侧调试面板的连接器状态（应显示"✅ 已连接"）
3. 点击"测试连接"按钮，查看控制台输出的API状态

### 2. 页码跳转测试
1. 点击"测试跳转"按钮
2. 观察文档是否有变化（文本选择、高亮等）
3. 查看控制台输出的详细执行日志

### 3. 建议选择测试
1. 点击任意建议卡片
2. 观察：
   - 卡片是否变为选中状态（蓝色边框）
   - 文档中是否出现高亮或选择
   - 右侧面板显示的当前页码

## 预期效果

### 成功情况
- ✅ 文档中出现黄色高亮区域
- ✅ 文本被选中（蓝色选择框）
- ✅ 页面滚动到对应位置
- ✅ 控制台显示"文本选择成功"、"高亮设置成功"

### 部分成功情况
- 🔄 文档中插入了标记文本（如"[页码1建议#1]"）
- 🔄 控制台显示API调用但无明显视觉变化
- 🔄 页面有轻微滚动或变化

### 失败情况
- ❌ 控制台显示"Api对象不存在"
- ❌ 所有API方法都返回错误
- ❌ 文档完全无变化

## 调试信息

### 关键控制台输出
```
=== 开始直接页码跳转 ===
使用callCommand进行直接跳转...
callCommand执行开始
Api对象存在，开始操作...
文档对象获取成功
文档元素总数: X
目标段落索引: Y
找到目标段落，开始选择文本...
文本范围: A - B
文本选择成功
高亮设置成功
```

### API状态检查
在"测试连接"中查看：
- `callCommand: ✅ 可用` - 最重要的API
- `window.Api: ✅ 可用` - 文档操作API
- `window.DocsAPI: ✅ 可用` - 编辑器API

## 故障排除

### 1. 连接器未初始化
**症状**: 显示"❌ 未连接"
**解决**: 刷新页面，等待更长时间让OnlyOffice完全加载

### 2. callCommand不可用
**症状**: 控制台显示"callCommand方法不可用"
**解决**: 检查OnlyOffice版本和配置，确保支持Automation API

### 3. Api对象不存在
**症状**: 控制台显示"Api对象不存在"
**解决**: 
- 检查OnlyOffice服务器配置
- 确认文档已完全加载
- 尝试刷新页面

### 4. 文档无变化但API调用成功
**症状**: 控制台显示成功但文档无视觉变化
**可能原因**: 
- 段落索引或文本范围不正确
- 文档内容与预期不符
- 高亮颜色与背景相似

## 技术实现

### 核心方法
1. **performDirectPageJump()** - 主要跳转逻辑
2. **callCommand()** - OnlyOffice文档操作API
3. **Api.GetDocument()** - 获取文档对象
4. **element.GetRange()** - 创建文本范围
5. **oRange.Select()** - 选择文本
6. **oRange.SetHighlight()** - 设置高亮

### 数据结构
每个建议包含：
- `pageNumber`: 目标页码
- `paragraphIndex`: 段落索引（用于定位）
- `startIndex/endIndex`: 文本范围
- `coordinates`: 坐标信息（x1,y1,x2,y2）
- `color`: 高亮颜色

## 下一步优化

1. **精确页码跳转** - 实现真正的页码API调用
2. **坐标定位** - 使用coordinates进行精确定位
3. **滚动优化** - 确保目标区域在可视范围内
4. **错误恢复** - 更好的错误处理和用户反馈

## 测试建议

1. **逐步测试** - 先测试连接，再测试跳转，最后测试建议选择
2. **查看日志** - 始终关注浏览器控制台的详细输出
3. **多次尝试** - 某些API调用可能需要文档完全加载后才能成功
4. **不同建议** - 测试不同页码和位置的建议

当前实现已经能够在OnlyOffice API可用的情况下实现基本的文档定位和高亮功能。如果遇到问题，请查看控制台输出并按照故障排除指南进行调试。
