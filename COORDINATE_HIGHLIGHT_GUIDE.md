# OnlyOffice 坐标高亮功能测试指南

## 🎯 功能概述

已实现基于坐标的精确高亮和页码跳转功能：

### 核心功能
1. **页码跳转** - 自动跳转到指定页码
2. **坐标标记** - 在指定坐标创建可视化标记
3. **文本高亮** - 高亮目标文本区域
4. **多重标记** - 矩形标记 + 文本标记 + 段落高亮

## 🔧 测试方法

### 1. 基础测试
```
点击"测试连接" → 查看API状态
点击"测试坐标" → 测试坐标标记功能
点击"测试跳转" → 测试页码跳转
```

### 2. 建议选择测试
```
点击任意建议卡片 → 观察文档变化
```

## 📊 预期效果

### ✅ 成功标志
1. **页码跳转成功**
   - 文档滚动到对应页面
   - 控制台显示"GoToPage调用成功"或类似信息

2. **坐标标记成功**
   - 文档中出现黄色半透明矩形
   - 插入坐标信息文本：`【坐标区域: (x1,y1) → (x2,y2)】`
   - 目标段落被高亮显示

3. **文本选择成功**
   - 指定文本范围被选中（蓝色选择框）
   - 文本背景变为黄色高亮

### 🔄 部分成功
- 只有文本标记出现，没有矩形标记
- 只有段落高亮，没有精确坐标标记
- 页码跳转不明显但有文本变化

### ❌ 失败情况
- 文档完全无变化
- 控制台显示大量错误信息
- API调用全部失败

## 🐛 调试信息

### 关键控制台输出
```
=== 开始直接页码跳转 ===
使用callCommand进行直接跳转...
Api对象存在，开始操作...
GoToPage调用成功 / SetCurrentPage调用成功
开始基于坐标的高亮操作...
坐标信息: {x1: 100, y1: 200, x2: 300, y2: 250}
矩形尺寸: 200 x 50
坐标标记矩形创建成功
坐标文本标记插入成功
```

### 测试坐标功能输出
```
=== 坐标高亮测试开始 ===
测试坐标: {x1: 100, y1: 200, x2: 300, y2: 250}
SelectByCoordinates 调用成功 / 方法不存在
坐标标记形状创建成功
坐标文本标记插入成功
段落高亮测试成功
```

## 🔍 技术实现

### 页码跳转方法
1. `oDocument.GoToPage(pageNumber - 1)`
2. `oDocument.SetCurrentPage(pageNumber - 1)`
3. `Api.GoToPage(pageNumber - 1)`
4. 段落估算定位（降级方案）

### 坐标标记方法
1. **矩形标记**
   ```javascript
   var oShape = Api.CreateShape("rect", width * 635, height * 635);
   oShape.SetFill(Api.CreateSolidFill(Api.CreateRGBColor(255, 255, 0)));
   ```

2. **文本标记**
   ```javascript
   var oRun = Api.CreateRun();
   oRun.AddText("【坐标区域: (x1,y1) → (x2,y2)】");
   oRun.SetHighlight("yellow");
   ```

3. **段落高亮**
   ```javascript
   var oRange = element.GetRange(startPos, endPos);
   oRange.Select();
   oRange.SetHighlight("yellow");
   ```

## 📋 测试清单

### 基础功能测试
- [ ] 连接器状态显示"✅ 已连接"
- [ ] "测试连接"显示API可用性
- [ ] "测试坐标"在文档中创建标记
- [ ] "测试跳转"执行页码跳转

### 建议选择测试
- [ ] 点击建议卡片变为选中状态
- [ ] 文档跳转到对应页码
- [ ] 出现坐标区域标记
- [ ] 插入坐标信息文本
- [ ] 目标文本被高亮

### 视觉效果检查
- [ ] 黄色半透明矩形标记
- [ ] 粗体黄色坐标文本
- [ ] 蓝色文本选择框
- [ ] 黄色文本背景高亮

## 🚀 使用建议

1. **按顺序测试**
   ```
   测试连接 → 测试坐标 → 测试跳转 → 选择建议
   ```

2. **观察文档变化**
   - 注意文档滚动
   - 查找新增的标记元素
   - 观察文本选择状态

3. **查看控制台**
   - 关注成功/失败信息
   - 记录API调用结果
   - 注意错误提示

4. **多次测试**
   - 不同的建议卡片
   - 不同的页码和坐标
   - 刷新页面重新测试

## 🔧 故障排除

### 问题1: 没有可视化标记
**可能原因**: 坐标值不正确或API不支持形状创建
**解决方案**: 查看是否有文本标记，检查控制台错误信息

### 问题2: 页码跳转不明显
**可能原因**: 文档较短或页码跳转API不可用
**解决方案**: 查看是否有文本选择变化，检查段落定位是否成功

### 问题3: 文本标记乱码
**可能原因**: 字符编码问题
**解决方案**: 查看控制台是否有坐标信息输出

### 问题4: 完全无效果
**可能原因**: callCommand API不可用
**解决方案**: 检查"测试连接"结果，确认OnlyOffice配置

## 📈 成功率预期

- **文本标记插入**: 90% 成功率
- **段落高亮**: 85% 成功率  
- **页码跳转**: 70% 成功率
- **矩形标记**: 60% 成功率
- **精确坐标选择**: 40% 成功率

即使部分功能不可用，也应该能看到明显的文档变化和标记效果。
