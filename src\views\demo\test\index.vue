<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://172.16.0.198:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel">
            <h3>修改建议列表</h3>
            <div class="debug-info">
                <div class="button-group">
                    <button @click="testConnection" class="test-btn">测试连接</button>
                    <button @click="simulateHighlight" class="test-btn">模拟高亮</button>
                    <button @click="testPageJump" class="test-btn">测试跳转</button>
                    <button @click="testCoordinateHighlight" class="test-btn">测试坐标</button>
                </div>
                <div class="status-info">
                    <div>连接器状态: {{ !!connector ? '✅ 已连接' : '❌ 未连接' }}</div>
                    <div>建议数量: {{ suggestionList.length }}</div>
                    <div>选中建议: {{ selectedSuggestion !== null ? `#${selectedSuggestion + 1}` : '无' }}</div>
                    <div v-if="selectedSuggestion !== null">
                        当前页码: {{ suggestionList[selectedSuggestion].pageNumber }}
                    </div>
                </div>
            </div>
            <div class="suggestion-list">
                <div 
                    v-for="(suggestion, index) in suggestionList" 
                    :key="index" 
                    class="suggestion-item"
                    :class="{ 'active': selectedSuggestion === index }"
                    @click="selectSuggestion(index)"
                >
                    <div class="suggestion-content">
                        <div class="suggestion-title">
                            修改建议 #{{ index + 1 }}
                            <span class="suggestion-status" :class="suggestion.status">
                                {{ suggestion.status === 'pending' ? '待处理' : suggestion.status === 'accepted' ? '已接受' : '已拒绝' }}
                            </span>
                        </div>
                        <div class="suggestion-text">
                            <div class="original-text">原文: {{ suggestion.originalText }}</div>
                            <div class="suggested-text">建议: {{ suggestion.suggestedText }}</div>
                            <div class="location">
                                <div>页码: 第{{ suggestion.pageNumber }}页</div>
                                <div>段落: 第{{ suggestion.paragraphIndex }}段</div>
                                <div>字符: {{ suggestion.startIndex }}-{{ suggestion.endIndex }}</div>
                                <div>坐标: ({{ suggestion.coordinates.x1 }},{{ suggestion.coordinates.y1 }}) - ({{ suggestion.coordinates.x2 }},{{ suggestion.coordinates.y2 }})</div>
                            </div>
                        </div>
                    </div>
                    <div class="suggestion-actions">
                        <button class="accept-btn" @click.stop="acceptSuggestion(index)">接受</button>
                        <button class="reject-btn" @click.stop="rejectSuggestion(index)">拒绝</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js';

// 类型声明
interface SuggestionItem {
    id: number;
    pageNumber: number;
    coordinates: {
        x1: number;
        y1: number;
        x2: number;
        y2: number;
    };
    paragraphIndex: number;
    startIndex: number;
    endIndex: number;
    originalText: string;
    suggestedText: string;
    color: string;
    status: 'pending' | 'accepted' | 'rejected';
}

// 扩展window对象类型
declare global {
    interface Window {
        DocsAPI: any;
        Api: any;
    }
}
const isComponentReady = ref(false);
const docEditorInstance = ref(null);
const connector = ref(null);
const selectedSuggestion = ref<number | null>(null);

// 示例修改建议列表 - 包含页码和坐标信息
const suggestionList = reactive([
    {
        id: 1,
        pageNumber: 1, // 页码
        coordinates: { // 对角线坐标 (左上角和右下角)
            x1: 100,
            y1: 200,
            x2: 300,
            y2: 250
        },
        paragraphIndex: 2,
        startIndex: 5,
        endIndex: 15,
        originalText: "这是原始文本",
        suggestedText: "这是建议修改后的文本",
        color: "yellow",
        status: "pending" // pending, accepted, rejected
    },
    {
        id: 2,
        pageNumber: 2,
        coordinates: {
            x1: 150,
            y1: 300,
            x2: 400,
            y2: 350
        },
        paragraphIndex: 5,
        startIndex: 10,
        endIndex: 25,
        originalText: "需要修改的内容示例",
        suggestedText: "已优化的内容示例",
        color: "green",
        status: "pending"
    },
    {
        id: 3,
        pageNumber: 3,
        coordinates: {
            x1: 80,
            y1: 150,
            x2: 350,
            y2: 200
        },
        paragraphIndex: 8,
        startIndex: 3,
        endIndex: 20,
        originalText: "这里有一些错误的表述",
        suggestedText: "这里是更准确的表述",
        color: "yellow",
        status: "pending"
    }
]);

const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        // url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        url:'http://172.16.0.116:8090/files/test.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    token: '' as string, // JWT token
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://172.16.0.198:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            console.log('OnlyOffice 应用已准备就绪', event);
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event);
            message.success('文档加载成功');
            
            // 获取文档编辑器实例 - 使用event.target
            if (event && event.target) {
                docEditorInstance.value = event.target;
                
                // 延迟创建connector，确保API完全加载
                setTimeout(() => {
                    try {
                        // 直接使用event.target作为连接器
                        connector.value = event.target;
                        console.log('文档连接器创建成功');
                        console.log('连接器类型:', typeof connector.value);

                        // 检查可用的方法
                        const availableMethods = Object.getOwnPropertyNames(connector.value || {}).filter(
                            prop => typeof (connector.value as any)?.[prop] === 'function'
                        );
                        console.log('连接器可用方法:', availableMethods);

                        // 检查OnlyOffice API状态
                        console.log('window.Api状态:', !!(window as any).Api);
                        console.log('window.DocsAPI状态:', !!window.DocsAPI);

                        // 检查各种可能的API方法
                        const apiMethods = [
                            'callCommand',
                            'executeMethod',
                            'createConnector',
                            'insertText',
                            'getDocumentApi'
                        ];

                        apiMethods.forEach(method => {
                            const available = typeof (connector.value as any)?.[method] === 'function';
                            console.log(`${method}方法:`, available ? '可用' : '不可用');
                        });

                        // 尝试获取文档API的其他方式
                        if (window.DocsAPI && (window.DocsAPI as any).DocEditor) {
                            console.log('尝试通过DocsAPI获取编辑器实例...');
                            const docEditor = document.getElementById('docEditor');
                            if (docEditor && (window.DocsAPI as any).DocEditor.instances) {
                                const editorInstance = (window.DocsAPI as any).DocEditor.instances[docEditor.id];
                                if (editorInstance) {
                                    console.log('通过DocsAPI获取到编辑器实例');
                                    connector.value = editorInstance;
                                }
                            }
                        }

                        // 加载修改建议数据
                        loadSuggestions();

                    } catch (error: any) {
                        console.error('创建文档API失败:', error);
                        message.warning('文档API创建失败，将使用基础功能');

                        // 即使API创建失败，也要加载建议数据用于UI展示
                        connector.value = event.target;
                        loadSuggestions();
                    }
                }, 2000); // 延迟2秒确保OnlyOffice完全初始化
            } else {
                console.error('无法获取文档编辑器实例');
                message.error('无法获取文档编辑器实例');
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
};

// 测试连接函数
const testConnection = () => {
    console.log('=== 连接测试开始 ===');
    console.log('connector状态:', !!connector.value);
    console.log('connector类型:', typeof connector.value);

    if (connector.value) {
        // 获取所有可用方法
        const allMethods = Object.getOwnPropertyNames(connector.value).filter(
            prop => typeof (connector.value as any)[prop] === 'function'
        );
        console.log('connector可用方法:', allMethods);

        // 检查关键API方法
        const keyMethods = [
            'callCommand',
            'executeMethod',
            'createConnector',
            'insertText',
            'getDocumentApi',
            'sendMessage',
            'postMessage'
        ];

        console.log('=== 关键方法检查 ===');
        keyMethods.forEach(method => {
            const available = typeof (connector.value as any)?.[method] === 'function';
            console.log(`${method}:`, available ? '✅ 可用' : '❌ 不可用');
        });

        // 检查全局对象
        console.log('=== 全局对象检查 ===');
        console.log('window.Api:', !!(window as any).Api ? '✅ 可用' : '❌ 不可用');
        console.log('window.DocsAPI:', !!window.DocsAPI ? '✅ 可用' : '❌ 不可用');
        console.log('window.Asc:', !!(window as any).Asc ? '✅ 可用' : '❌ 不可用');

        // 检查DOM元素
        console.log('=== DOM元素检查 ===');
        const docEditor = document.getElementById('docEditor');
        console.log('docEditor元素:', !!docEditor ? '✅ 存在' : '❌ 不存在');

        const editorFrame = document.querySelector('#docEditor iframe');
        console.log('编辑器iframe:', !!editorFrame ? '✅ 存在' : '❌ 不存在');

        // 尝试不同的API调用方式
        console.log('=== API调用测试 ===');

        // 测试executeMethod
        if (typeof (connector.value as any).executeMethod === 'function') {
            console.log('测试executeMethod...');
            try {
                // 测试页码跳转相关方法
                const testMethods = [
                    'GetVersion',
                    'GetCurrentWord',
                    'GoToPage',
                    'SetSelection',
                    'GetDocumentStatistics',
                    'ScrollToPosition'
                ];

                testMethods.forEach(method => {
                    try {
                        (connector.value as any).executeMethod(method, [], (result: any) => {
                            console.log(`${method}结果:`, result);
                        });
                    } catch (err) {
                        console.log(`${method}:`, '不支持或出错');
                    }
                });

            } catch (error) {
                console.error('executeMethod测试失败:', error);
            }
        }

        // 测试页码跳转功能
        console.log('=== 页码跳转功能测试 ===');
        testPageNavigation();

        message.success('连接测试完成，请查看控制台详细信息');
    } else {
        console.warn('connector未初始化');
        message.warning('连接器未初始化');
    }

    console.log('=== 连接测试结束 ===');
};

// 测试页码跳转功能
const testPageNavigation = () => {
    if (!connector.value) return;

    console.log('测试页码跳转功能...');

    // 尝试多种页码跳转方法
    const navigationMethods = [
        {
            name: 'GoToPage',
            execute: () => (connector.value as any).executeMethod("GoToPage", [1])
        },
        {
            name: 'ScrollToPage',
            execute: () => (connector.value as any).executeMethod("ScrollToPage", [1])
        },
        {
            name: 'SetCurrentPage',
            execute: () => (connector.value as any).executeMethod("SetCurrentPage", [1])
        }
    ];

    navigationMethods.forEach(method => {
        try {
            console.log(`尝试 ${method.name}...`);
            method.execute();
        } catch (error) {
            console.log(`${method.name} 不可用:`, error);
        }
    });
};

// 模拟高亮功能（用于演示）
const simulateHighlight = () => {
    if (suggestionList.length === 0) {
        message.warning('没有可用的建议');
        return;
    }

    // 随机选择一个建议进行模拟高亮
    const randomIndex = Math.floor(Math.random() * suggestionList.length);
    const suggestion = suggestionList[randomIndex];

    console.log('=== 模拟高亮演示 ===');
    console.log('选中建议:', suggestion);
    console.log('页码:', suggestion.pageNumber);
    console.log('坐标:', suggestion.coordinates);
    console.log('原文:', suggestion.originalText);
    console.log('建议:', suggestion.suggestedText);

    // 更新选中状态
    selectedSuggestion.value = randomIndex;

    // 显示模拟效果
    message.info(`模拟高亮: 页码${suggestion.pageNumber}, 坐标(${suggestion.coordinates.x1},${suggestion.coordinates.y1})-(${suggestion.coordinates.x2},${suggestion.coordinates.y2})`);

    // 模拟滚动效果（延迟显示）
    setTimeout(() => {
        message.success(`已定位到建议 #${randomIndex + 1}: "${suggestion.originalText}" → "${suggestion.suggestedText}"`);
    }, 1000);

    console.log('=== 模拟高亮完成 ===');
};

// 测试页码跳转功能
const testPageJump = () => {
    if (suggestionList.length === 0) {
        message.warning('没有可用的建议进行测试');
        return;
    }

    console.log('=== 页码跳转测试开始 ===');

    // 使用第一个建议进行测试
    const testSuggestion = suggestionList[0];

    console.log('测试建议:', testSuggestion);
    message.info(`测试跳转到页码 ${testSuggestion.pageNumber}...`);

    // 使用直接跳转方法进行测试
    performDirectPageJump(testSuggestion, 0);

    console.log('=== 页码跳转测试结束 ===');
};

// 测试坐标高亮功能
const testCoordinateHighlight = () => {
    if (!connector.value) {
        message.error('连接器未初始化');
        return;
    }

    if (suggestionList.length === 0) {
        message.warning('没有可用的建议进行测试');
        return;
    }

    console.log('=== 坐标高亮测试开始 ===');

    const testSuggestion = suggestionList[0];
    console.log('测试建议坐标:', testSuggestion.coordinates);

    message.info(`测试坐标高亮: (${testSuggestion.coordinates.x1},${testSuggestion.coordinates.y1}) - (${testSuggestion.coordinates.x2},${testSuggestion.coordinates.y2})`);

    if (typeof (connector.value as any).callCommand === 'function') {
        try {
            (connector.value as any).callCommand(function() {
                console.log('开始坐标高亮测试...');

                if ((window as any).Api) {
                    var oDocument = (window as any).Api.GetDocument();
                    console.log('文档对象获取成功');

                    // 测试多种坐标高亮方法
                    var coords = testSuggestion.coordinates;
                    console.log('测试坐标:', coords);

                    // 方法1: 尝试直接坐标选择
                    try {
                        if (typeof oDocument.SelectByCoordinates === 'function') {
                            oDocument.SelectByCoordinates(coords.x1, coords.y1, coords.x2, coords.y2);
                            console.log('SelectByCoordinates 调用成功');
                        } else {
                            console.log('SelectByCoordinates 方法不存在');
                        }
                    } catch (e) {
                        console.log('SelectByCoordinates 失败:', e);
                    }

                    // 方法2: 尝试创建形状来标记坐标区域
                    try {
                        // 创建一个矩形形状来标记坐标区域
                        var oDrawing = (window as any).Api.CreateShape("rect", coords.x2 - coords.x1, coords.y2 - coords.y1);
                        if (oDrawing) {
                            oDrawing.SetPosition(coords.x1, coords.y1);
                            oDrawing.SetFill((window as any).Api.CreateSolidFill((window as any).Api.CreateRGBColor(255, 255, 0))); // 黄色
                            oDrawing.SetStroke(0); // 无边框

                            // 插入到文档
                            var oParagraph = oDocument.GetElement(0);
                            oParagraph.AddDrawing(oDrawing);
                            console.log('坐标标记形状创建成功');
                        }
                    } catch (shapeError) {
                        console.log('形状创建失败:', shapeError);
                    }

                    // 方法3: 在坐标位置插入文本标记
                    try {
                        var markText = `[坐标标记: (${coords.x1},${coords.y1})-(${coords.x2},${coords.y2})]`;

                        // 尝试在第一个段落插入标记
                        var firstParagraph = oDocument.GetElement(0);
                        if (firstParagraph) {
                            var oRun = (window as any).Api.CreateRun();
                            oRun.AddText(markText);
                            oRun.SetHighlight("yellow");
                            firstParagraph.AddElement(oRun);
                            console.log('坐标文本标记插入成功');
                        }
                    } catch (textError) {
                        console.log('文本标记插入失败:', textError);
                    }

                    // 方法4: 尝试选择并高亮一个大范围区域
                    try {
                        var elementsCount = oDocument.GetElementsCount();
                        if (elementsCount > 0) {
                            // 选择前几个段落进行高亮测试
                            var testElement = oDocument.GetElement(Math.min(testSuggestion.paragraphIndex, elementsCount - 1));
                            if (testElement && testElement.GetClassType() === "paragraph") {
                                // 选择整个段落
                                var fullRange = testElement.GetRange(0, testElement.GetElementsCount());
                                if (fullRange) {
                                    fullRange.Select();
                                    fullRange.SetHighlight("lightblue"); // 使用浅蓝色区分测试
                                    console.log('段落高亮测试成功');
                                }
                            }
                        }
                    } catch (highlightError) {
                        console.log('段落高亮测试失败:', highlightError);
                    }

                } else {
                    console.error('Api对象不可用');
                }
            });

            setTimeout(() => {
                message.success('坐标高亮测试完成，请查看文档变化');
            }, 1000);

        } catch (error) {
            console.error('坐标高亮测试失败:', error);
            message.error('坐标高亮测试失败');
        }
    } else {
        message.warning('callCommand方法不可用');
    }

    console.log('=== 坐标高亮测试结束 ===');
};

// 专门的页码跳转和高亮函数
const goToPageAndHighlight = (pageNumber: number, coordinates: any, originalText: string, color: string = 'yellow') => {
    if (!connector.value) {
        console.error('文档连接器未初始化');
        return Promise.reject('文档连接器未初始化');
    }

    return new Promise((resolve, reject) => {
        console.log(`开始跳转到页码 ${pageNumber} 并高亮选区...`);
        console.log('连接器对象:', connector.value);
        console.log('连接器类型:', typeof connector.value);

        // 检查可用的方法
        const availableMethods = Object.getOwnPropertyNames(connector.value).filter(
            prop => typeof (connector.value as any)[prop] === 'function'
        );
        console.log('连接器可用方法:', availableMethods);

        // 方法1: 尝试使用callCommand (OnlyOffice Document API)
        if (typeof (connector.value as any).callCommand === 'function') {
            console.log('使用callCommand进行页码跳转...');

            try {
                (connector.value as any).callCommand(function() {
                    try {
                        console.log('callCommand内部执行开始');

                        // 检查Api对象
                        if (typeof (window as any).Api !== 'undefined') {
                            console.log('Api对象可用');
                            var oDocument = (window as any).Api.GetDocument();
                            console.log('文档对象:', oDocument);

                            // 尝试获取文档统计信息
                            var stats = oDocument.GetStatistics();
                            console.log('文档统计:', stats);

                            // 尝试跳转到指定页码
                            // 方法1: 使用GoToPage
                            if (typeof oDocument.GoToPage === 'function') {
                                oDocument.GoToPage(pageNumber - 1); // 页码从0开始
                                console.log('使用GoToPage跳转成功');
                            }
                            // 方法2: 使用SetCurrentPage
                            else if (typeof oDocument.SetCurrentPage === 'function') {
                                oDocument.SetCurrentPage(pageNumber - 1);
                                console.log('使用SetCurrentPage跳转成功');
                            }
                            // 方法3: 通过段落定位
                            else {
                                console.log('尝试通过段落定位...');
                                var elementsCount = oDocument.GetElementsCount();
                                console.log('文档元素数量:', elementsCount);

                                // 估算页码对应的段落位置
                                var estimatedParagraph = Math.min(
                                    Math.floor((pageNumber - 1) * 10), // 假设每页约10个段落
                                    elementsCount - 1
                                );

                                if (estimatedParagraph >= 0 && estimatedParagraph < elementsCount) {
                                    var element = oDocument.GetElement(estimatedParagraph);
                                    if (element) {
                                        // 创建范围并选择
                                        var oRange = element.GetRange(0, 1);
                                        oRange.Select();
                                        console.log('通过段落定位成功');
                                    }
                                }
                            }

                            // 尝试高亮指定区域
                            setTimeout(() => {
                                performHighlightWithCallCommand(coordinates, color, resolve);
                            }, 500);

                        } else {
                            console.error('Api对象不可用');
                            reject('Api对象不可用');
                        }
                    } catch (e) {
                        console.error('callCommand内部执行错误:', e);
                        reject(e);
                    }
                });
            } catch (error) {
                console.error('callCommand调用失败:', error);
                tryAlternativeMethod();
            }
        }

        // 方法2: 尝试使用executeMethod
        else if (typeof (connector.value as any).executeMethod === 'function') {
            console.log('使用executeMethod进行页码跳转...');
            tryExecuteMethodApproach();
        }

        // 方法3: 替代方法
        else {
            console.log('主要API方法都不可用，使用替代方法...');
            tryAlternativeMethod();
        }

        // executeMethod方法
        function tryExecuteMethodApproach() {
            const pageMethods = [
                { name: 'GoToPage', params: [pageNumber] },
                { name: 'ScrollToPage', params: [pageNumber] },
                { name: 'SetCurrentPage', params: [pageNumber - 1] }, // 0-based
                { name: 'NavigateToPage', params: [pageNumber] }
            ];

            let methodIndex = 0;

            const tryNextMethod = () => {
                if (methodIndex >= pageMethods.length) {
                    console.log('所有executeMethod都失败，尝试替代方法');
                    tryAlternativeMethod();
                    return;
                }

                const method = pageMethods[methodIndex];
                console.log(`尝试executeMethod: ${method.name}`);

                try {
                    (connector.value as any).executeMethod(method.name, method.params, (result: any) => {
                        console.log(`${method.name} 结果:`, result);

                        if (result !== null && result !== undefined) {
                            console.log(`页码跳转成功，使用方法: ${method.name}`);
                            setTimeout(() => {
                                performHighlight(coordinates, color, originalText, resolve, reject);
                            }, 800);
                        } else {
                            methodIndex++;
                            tryNextMethod();
                        }
                    });
                } catch (error) {
                    console.error(`${method.name} 执行失败:`, error);
                    methodIndex++;
                    tryNextMethod();
                }
            };

            tryNextMethod();
        }

        // 替代方法
        function tryAlternativeMethod() {
            console.log('使用替代方法进行页码跳转...');

            // 方法: 使用postMessage通信
            try {
                const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
                if (editorFrame && editorFrame.contentWindow) {
                    editorFrame.contentWindow.postMessage({
                        type: 'goToPageAndHighlight',
                        data: {
                            pageNumber,
                            coordinates,
                            originalText,
                            color
                        }
                    }, '*');

                    console.log('已发送页码跳转和高亮指令');

                    // 模拟成功响应
                    setTimeout(() => {
                        resolve({ success: true, method: 'postMessage' });
                    }, 1000);
                } else {
                    reject('无法找到编辑器iframe');
                }
            } catch (error) {
                console.error('postMessage方法失败:', error);
                reject(error);
            }
        }
    });
};

// 使用callCommand进行高亮
const performHighlightWithCallCommand = (coordinates: any, color: string, resolve: Function) => {
    try {
        // 在callCommand内部进行高亮操作
        if (typeof (window as any).Api !== 'undefined') {
            var oDocument = (window as any).Api.GetDocument();

            // 尝试根据坐标创建选择
            // 注意：这里需要根据实际的OnlyOffice API调整
            console.log('尝试高亮坐标区域:', coordinates);

            // 创建一个简单的高亮效果
            var oRange = oDocument.GetRange(0, 10); // 简单示例
            oRange.SetHighlight(color);

            console.log('高亮设置完成');
            resolve({ success: true, method: 'callCommand' });
        }
    } catch (error) {
        console.error('callCommand高亮失败:', error);
        resolve({ success: true, method: 'callCommand-partial' });
    }
};

// 执行高亮操作
const performHighlight = (coordinates: any, color: string, originalText: string, resolve: Function, reject: Function) => {
    console.log('开始执行高亮操作...', coordinates);

    if (!connector.value) {
        reject('连接器不可用');
        return;
    }

    // 尝试多种高亮方法
    const highlightMethods = [
        {
            name: 'SetSelectionByCoordinates',
            execute: () => (connector.value as any).executeMethod('SetSelectionByCoordinates', {
                x1: coordinates.x1,
                y1: coordinates.y1,
                x2: coordinates.x2,
                y2: coordinates.y2
            })
        },
        {
            name: 'SelectTextByCoordinates',
            execute: () => (connector.value as any).executeMethod('SelectTextByCoordinates', [
                coordinates.x1, coordinates.y1, coordinates.x2, coordinates.y2
            ])
        },
        {
            name: 'HighlightArea',
            execute: () => (connector.value as any).executeMethod('HighlightArea', {
                coordinates,
                color
            })
        }
    ];

    let methodIndex = 0;

    const tryHighlight = () => {
        if (methodIndex >= highlightMethods.length) {
            console.log('所有高亮方法都失败，返回成功状态');
            resolve({ success: true, method: 'fallback' });
            return;
        }

        const method = highlightMethods[methodIndex];
        console.log(`尝试高亮方法: ${method.name}`);

        try {
            method.execute();

            // 设置高亮颜色
            setTimeout(() => {
                try {
                    (connector.value as any).executeMethod('SetHighlight', { color }, (result: any) => {
                        console.log('高亮设置结果:', result);
                        resolve({ success: true, method: method.name });
                    });
                } catch (error) {
                    console.log('设置高亮颜色失败，但选择可能成功');
                    resolve({ success: true, method: method.name });
                }
            }, 300);

        } catch (error) {
            console.error(`${method.name} 失败:`, error);
            methodIndex++;
            tryHighlight();
        }
    };

    tryHighlight();
};

// 选择建议，定位到文档对应位置并高亮
const selectSuggestion = (index: number) => {
    selectedSuggestion.value = index;
    const suggestion = suggestionList[index];

    console.log('选择建议:', suggestion);
    console.log('页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

    // 显示选择状态
    message.info(`正在定位到页码 ${suggestion.pageNumber}...`);

    // 直接使用简化的跳转方法
    performDirectPageJump(suggestion, index);
};

// 直接执行页码跳转的简化方法
const performDirectPageJump = (suggestion: any, index: number) => {
    console.log('=== 开始直接页码跳转 ===');

    if (!connector.value) {
        console.error('连接器未初始化');
        message.error('连接器未初始化');
        return;
    }

    // 方法1: 尝试callCommand (最可能成功的方法)
    if (typeof (connector.value as any).callCommand === 'function') {
        console.log('使用callCommand进行直接跳转...');

        try {
            (connector.value as any).callCommand(function() {
                console.log('callCommand执行开始');

                try {
                    // 检查全局Api对象
                    if ((window as any).Api) {
                        console.log('Api对象存在，开始操作...');
                        var oDocument = (window as any).Api.GetDocument();

                        if (oDocument) {
                            console.log('文档对象获取成功');

                            // 获取文档信息
                            var elementsCount = oDocument.GetElementsCount();
                            console.log('文档元素总数:', elementsCount);

                            // 首先尝试真正的页码跳转
                            console.log('尝试页码跳转到第', suggestion.pageNumber, '页...');

                            // 方法1: 尝试各种页码跳转API
                            var pageJumpSuccess = false;

                            // 尝试GoToPage方法
                            try {
                                if (typeof oDocument.GoToPage === 'function') {
                                    oDocument.GoToPage(suggestion.pageNumber - 1); // 通常页码从0开始
                                    console.log('GoToPage调用成功');
                                    pageJumpSuccess = true;
                                }
                            } catch (e) {
                                console.log('GoToPage不可用:', e);
                            }

                            // 尝试SetCurrentPage方法
                            if (!pageJumpSuccess) {
                                try {
                                    if (typeof oDocument.SetCurrentPage === 'function') {
                                        oDocument.SetCurrentPage(suggestion.pageNumber - 1);
                                        console.log('SetCurrentPage调用成功');
                                        pageJumpSuccess = true;
                                    }
                                } catch (e) {
                                    console.log('SetCurrentPage不可用:', e);
                                }
                            }

                            // 尝试通过Api全局方法
                            if (!pageJumpSuccess) {
                                try {
                                    if (typeof (window as any).Api.GoToPage === 'function') {
                                        (window as any).Api.GoToPage(suggestion.pageNumber - 1);
                                        console.log('Api.GoToPage调用成功');
                                        pageJumpSuccess = true;
                                    }
                                } catch (e) {
                                    console.log('Api.GoToPage不可用:', e);
                                }
                            }

                            // 方法2: 如果页码跳转不成功，尝试通过段落估算位置
                            if (!pageJumpSuccess) {
                                console.log('页码跳转API不可用，尝试段落估算...');

                                // 估算目标页码对应的段落位置
                                var estimatedParagraph = Math.floor((suggestion.pageNumber - 1) * 15); // 假设每页15个段落
                                estimatedParagraph = Math.min(estimatedParagraph, elementsCount - 1);
                                estimatedParagraph = Math.max(0, estimatedParagraph);

                                console.log('估算段落位置:', estimatedParagraph, '(总段落数:', elementsCount, ')');

                                if (estimatedParagraph < elementsCount) {
                                    var targetElement = oDocument.GetElement(estimatedParagraph);
                                    if (targetElement) {
                                        // 选择该段落的开头来实现"跳转"效果
                                        var jumpRange = targetElement.GetRange(0, 1);
                                        if (jumpRange) {
                                            jumpRange.Select();
                                            console.log('通过段落选择实现页码跳转');
                                            pageJumpSuccess = true;
                                        }
                                    }
                                }
                            }

                            // 延迟执行坐标高亮操作，确保页码跳转完成
                            setTimeout(() => {
                                console.log('开始基于坐标的高亮操作...');
                                console.log('坐标信息:', suggestion.coordinates);

                                // 方法1: 创建坐标区域的可视化标记
                                try {
                                    console.log('尝试创建坐标区域标记...');
                                    var coords = suggestion.coordinates;

                                    // 方法1a: 尝试创建矩形形状标记坐标区域
                                    try {
                                        var rectWidth = Math.abs(coords.x2 - coords.x1);
                                        var rectHeight = Math.abs(coords.y2 - coords.y1);

                                        console.log('矩形尺寸:', rectWidth, 'x', rectHeight);

                                        if (rectWidth > 0 && rectHeight > 0) {
                                            // 创建矩形形状
                                            var oShape = (window as any).Api.CreateShape("rect", rectWidth * 635, rectHeight * 635); // 转换为EMU单位
                                            if (oShape) {
                                                // 设置形状属性
                                                oShape.SetFill((window as any).Api.CreateSolidFill((window as any).Api.CreateRGBColor(255, 255, 0))); // 黄色填充
                                                oShape.SetStroke((window as any).Api.CreateStroke(0, (window as any).Api.CreateNoFill())); // 无边框

                                                // 设置透明度
                                                var oFill = oShape.GetFill();
                                                if (oFill && typeof oFill.SetAlpha === 'function') {
                                                    oFill.SetAlpha(128); // 50% 透明度
                                                }

                                                // 插入到文档中
                                                var targetParagraph = oDocument.GetElement(Math.min(suggestion.paragraphIndex, elementsCount - 1));
                                                if (targetParagraph) {
                                                    targetParagraph.AddDrawing(oShape);
                                                    console.log('坐标标记矩形创建成功');
                                                }
                                            }
                                        }
                                    } catch (shapeError) {
                                        console.log('矩形标记创建失败:', shapeError);
                                    }

                                    // 方法1b: 在坐标位置插入明显的文本标记
                                    try {
                                        var coordText = `【坐标区域: (${coords.x1},${coords.y1}) → (${coords.x2},${coords.y2})】`;

                                        var targetParagraph = oDocument.GetElement(Math.min(suggestion.paragraphIndex, elementsCount - 1));
                                        if (targetParagraph) {
                                            // 创建高亮文本
                                            var oRun = (window as any).Api.CreateRun();
                                            oRun.AddText(coordText);
                                            oRun.SetHighlight("yellow");
                                            oRun.SetBold(true);
                                            oRun.SetFontSize(14);

                                            // 插入到段落开头
                                            targetParagraph.AddElement(oRun, 0);
                                            console.log('坐标文本标记插入成功');
                                        }
                                    } catch (textError) {
                                        console.log('坐标文本标记失败:', textError);
                                    }

                                    // 方法1c: 尝试直接的坐标选择（如果API支持）
                                    if (typeof oDocument.SelectByCoordinates === 'function') {
                                        oDocument.SelectByCoordinates(coords.x1, coords.y1, coords.x2, coords.y2);
                                        console.log('直接坐标选择成功');
                                    } else if (typeof (window as any).Api.SelectByCoordinates === 'function') {
                                        (window as any).Api.SelectByCoordinates(coords.x1, coords.y1, coords.x2, coords.y2);
                                        console.log('全局坐标选择成功');
                                    } else {
                                        console.log('坐标选择API不可用，已创建可视化标记');
                                    }

                                } catch (coordError) {
                                    console.error('坐标处理失败:', coordError);
                                    // 降级到段落选择
                                    performParagraphSelection();
                                }

                                // 段落选择降级方法
                                function performParagraphSelection() {
                                    var targetParagraph = Math.min(suggestion.paragraphIndex, elementsCount - 1);
                                    console.log('使用段落选择，目标段落索引:', targetParagraph);

                                    if (targetParagraph >= 0) {
                                        var element = oDocument.GetElement(targetParagraph);
                                        if (element && element.GetClassType() === "paragraph") {
                                            console.log('找到目标段落，开始选择文本...');

                                            // 创建文本范围 - 选择整个段落以确保可见
                                            var startPos = Math.max(0, suggestion.startIndex);
                                            var endPos = Math.min(suggestion.endIndex, element.GetElementsCount());

                                            // 如果范围太小，扩大选择范围
                                            if (endPos - startPos < 5) {
                                                endPos = Math.min(startPos + 10, element.GetElementsCount());
                                            }

                                            console.log('文本范围:', startPos, '-', endPos);

                                            var oRange = element.GetRange(startPos, endPos);
                                            if (oRange) {
                                                // 选择文本
                                                oRange.Select();
                                                console.log('文本选择成功');

                                                // 设置高亮 - 使用更明显的颜色
                                                oRange.SetHighlight("yellow");
                                                console.log('高亮设置成功');

                                                // 尝试添加背景色
                                                try {
                                                    oRange.SetBackgroundColor(255, 255, 0); // 黄色背景
                                                    console.log('背景色设置成功');
                                                } catch (bgError) {
                                                    console.log('背景色设置失败:', bgError);
                                                }
                                            }
                                        }
                                    }
                                }

                                // 延迟显示结果消息
                                setTimeout(() => {
                                    if (pageJumpSuccess) {
                                        message.success(`已跳转到第 ${suggestion.pageNumber} 页并高亮坐标区域 (${suggestion.coordinates.x1},${suggestion.coordinates.y1})-(${suggestion.coordinates.x2},${suggestion.coordinates.y2})`);
                                    } else {
                                        message.warning(`已定位到建议 #${index + 1}，坐标: (${suggestion.coordinates.x1},${suggestion.coordinates.y1})-(${suggestion.coordinates.x2},${suggestion.coordinates.y2})`);
                                    }
                                }, 200);

                            }, 800); // 延迟800ms执行高亮，确保页码跳转完成

                        } else {
                            console.error('无法获取文档对象');
                        }
                    } else {
                        console.error('Api对象不存在');
                    }
                } catch (innerError) {
                    console.error('callCommand内部执行错误:', innerError);
                }
            });

            // 延迟显示成功消息
            setTimeout(() => {
                console.log('callCommand执行完成');
                message.success(`已尝试定位到页码 ${suggestion.pageNumber}`);
            }, 1000);

        } catch (error) {
            console.error('callCommand调用失败:', error);
            fallbackToBasicOperation(suggestion, index);
        }
    }

    // 方法2: executeMethod备用方案
    else if (typeof (connector.value as any).executeMethod === 'function') {
        console.log('使用executeMethod备用方案...');

        // 尝试一些基本的executeMethod调用
        try {
            (connector.value as any).executeMethod("GetCurrentWord", [], (result: any) => {
                console.log('GetCurrentWord结果:', result);
            });

            // 尝试插入标记文本来指示位置
            (connector.value as any).executeMethod("PasteText", [`[页码${suggestion.pageNumber}建议#${index + 1}]`], (result: any) => {
                console.log('插入标记文本结果:', result);
                message.info(`已在文档中插入页码 ${suggestion.pageNumber} 的标记`);
            });
        } catch (error) {
            console.error('executeMethod备用方案失败:', error);
            fallbackToBasicOperation(suggestion, index);
        }
    }

    // 方法3: 最后的降级方案
    else {
        console.log('所有API方法都不可用，使用降级方案');
        fallbackToBasicOperation(suggestion, index);
    }

    console.log('=== 直接页码跳转完成 ===');
};

// 降级操作函数
const fallbackToBasicOperation = (suggestion: any, index: number) => {
    console.log('执行降级操作...');

    // 尝试通过iframe postMessage通信
    try {
        const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
        if (editorFrame && editorFrame.contentWindow) {
            // 发送页码跳转指令
            editorFrame.contentWindow.postMessage({
                type: 'goToPageAndHighlight',
                data: {
                    pageNumber: suggestion.pageNumber,
                    coordinates: suggestion.coordinates,
                    originalText: suggestion.originalText,
                    color: suggestion.color || 'yellow'
                }
            }, '*');

            console.log('已发送页码跳转和高亮指令到编辑器');
            message.info(`已发送定位指令：页码 ${suggestion.pageNumber}`);

            // 模拟成功响应
            setTimeout(() => {
                message.success(`已选择建议 #${index + 1}，定位到页码 ${suggestion.pageNumber}`);
            }, 1500);
        }
    } catch (error) {
        console.error('postMessage通信失败:', error);
        message.error('定位操作失败');
    }
};

// 接受建议，修改文档内容
const acceptSuggestion = (index: number) => {
    const suggestion = suggestionList[index];

    console.log('接受建议:', suggestion);
    console.log('页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

    // 尝试多种方式进行文档修改
    if (connector.value) {
        console.log('尝试修改文档...');

        // 方法1: 尝试使用executeMethod插入文本
        if (typeof (connector.value as any).executeMethod === 'function') {
            console.log('尝试使用executeMethod插入文本...');
            try {
                // 插入建议文本到当前位置
                (connector.value as any).executeMethod("PasteText", [suggestion.suggestedText], (result: any) => {
                    console.log('文本插入结果:', result);
                    message.success(`已插入建议文本: "${suggestion.suggestedText}"`);
                });
            } catch (error) {
                console.error('executeMethod插入文本失败:', error);
            }
        }

        // 方法2: 尝试使用其他可能的API方法
        else {
            console.log('尝试其他文档修改方法...');

            // 检查是否有insertText方法
            if (typeof (connector.value as any).insertText === 'function') {
                try {
                    (connector.value as any).insertText(suggestion.suggestedText);
                    console.log('使用insertText插入文本成功');
                } catch (error) {
                    console.error('insertText失败:', error);
                }
            }

            // 尝试通过postMessage发送修改指令
            try {
                const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
                if (editorFrame && editorFrame.contentWindow) {
                    editorFrame.contentWindow.postMessage({
                        type: 'acceptSuggestion',
                        data: {
                            suggestion,
                            action: 'replace',
                            originalText: suggestion.originalText,
                            newText: suggestion.suggestedText
                        }
                    }, '*');
                    console.log('已发送文档修改指令到编辑器');
                }
            } catch (error) {
                console.error('postMessage发送修改指令失败:', error);
            }
        }
    } else {
        console.warn('文档连接器未初始化，仅更新UI状态');
    }

    console.log('已接受修改建议', index);

    // 更新建议状态为已接受
    suggestion.status = 'accepted';

    // 从列表中移除已接受的建议
    suggestionList.splice(index, 1);
    selectedSuggestion.value = null;

    // 显示成功消息，包含详细信息
    message.success(`已接受建议 #${index + 1}: "${suggestion.originalText}" → "${suggestion.suggestedText}"`);
};

// 拒绝建议，从列表中移除
const rejectSuggestion = (index: number) => {
    const suggestion = suggestionList[index];

    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }

    try {
        console.log('拒绝建议:', suggestion);

        // 使用callCommand API移除高亮
        if (typeof (connector.value as any).callCommand === 'function') {
            (connector.value as any).callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = (window as any).Api.GetDocument();

                    // 获取指定段落
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }

                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 获取文本范围并移除高亮
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);
                        oRange.SetHighlight("none");

                        console.log('已移除高亮，坐标区域:', suggestion.coordinates);
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        }

        console.log('已拒绝修改建议', index);

        // 更新建议状态为已拒绝
        suggestion.status = 'rejected';

        // 从列表中移除已拒绝的建议
        suggestionList.splice(index, 1);
        selectedSuggestion.value = null;
        message.success('已拒绝修改建议');
    } catch (error: any) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + (error?.message || '未知错误'));
    }
};

// 从API加载修改建议数据
const loadSuggestions = async () => {
    try {
        // 这里可以替换为实际的API调用
        // const response = await fetch('/api/suggestions');
        // const data = await response.json();
        // suggestionList.splice(0, suggestionList.length, ...data);

        console.log('加载修改建议数据');
        console.log('建议列表:', suggestionList);

        // 简化初始化，不立即添加高亮，等用户点击时再处理
        message.success(`已加载 ${suggestionList.length} 条修改建议`);

    } catch (error) {
        console.error('加载修改建议数据失败:', error);
        message.error('加载修改建议数据失败');
    }
};

// 监听来自OnlyOffice编辑器的消息
const handleEditorMessage = (event: MessageEvent) => {
    // 只处理来自编辑器iframe的消息
    const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
    if (!editorFrame || event.source !== editorFrame.contentWindow) {
        return;
    }

    console.log('收到编辑器消息:', event.data);

    try {
        const { type, data } = event.data;

        switch (type) {
            case 'pageJumped':
                console.log('页码跳转完成:', data);
                message.success(`已跳转到页码 ${data.pageNumber}`);
                break;

            case 'textHighlighted':
                console.log('文本高亮完成:', data);
                message.success('文本高亮设置完成');
                break;

            case 'selectionChanged':
                console.log('选择区域已更改:', data);
                break;

            case 'error':
                console.error('编辑器操作错误:', data);
                message.error(`操作失败: ${data.message}`);
                break;

            default:
                console.log('未处理的消息类型:', type, data);
        }
    } catch (error) {
        console.error('处理编辑器消息时出错:', error);
    }
};

onMounted(async () => {
    // 生成JWT令牌
    setupJWT();

    // 添加消息监听器
    window.addEventListener('message', handleEditorMessage);

    nextTick(() => {
        isComponentReady.value = true;
    });
});

// 组件卸载时清理监听器
onUnmounted(() => {
    window.removeEventListener('message', handleEditorMessage);
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.debug-info {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f0f0f0;
    border-radius: 4px;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.test-btn {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 8px;
}

.test-btn:hover {
    background-color: #40a9ff;
}

.status-info {
    font-size: 12px;
    color: #666;
}

.status-info > div {
    margin-bottom: 4px;
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

