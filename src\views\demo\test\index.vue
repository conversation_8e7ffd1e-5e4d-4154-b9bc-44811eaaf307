<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://172.16.0.198:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel">
            <h3>修改建议列表</h3>
            <div class="debug-info">
                <button @click="testConnection" class="test-btn">测试连接</button>
                <button @click="simulateHighlight" class="test-btn">模拟高亮</button>
                <div class="status-info">
                    <div>连接器状态: {{ !!connector ? '已连接' : '未连接' }}</div>
                    <div>建议数量: {{ suggestionList.length }}</div>
                    <div>选中建议: {{ selectedSuggestion !== null ? `#${selectedSuggestion + 1}` : '无' }}</div>
                </div>
            </div>
            <div class="suggestion-list">
                <div 
                    v-for="(suggestion, index) in suggestionList" 
                    :key="index" 
                    class="suggestion-item"
                    :class="{ 'active': selectedSuggestion === index }"
                    @click="selectSuggestion(index)"
                >
                    <div class="suggestion-content">
                        <div class="suggestion-title">
                            修改建议 #{{ index + 1 }}
                            <span class="suggestion-status" :class="suggestion.status">
                                {{ suggestion.status === 'pending' ? '待处理' : suggestion.status === 'accepted' ? '已接受' : '已拒绝' }}
                            </span>
                        </div>
                        <div class="suggestion-text">
                            <div class="original-text">原文: {{ suggestion.originalText }}</div>
                            <div class="suggested-text">建议: {{ suggestion.suggestedText }}</div>
                            <div class="location">
                                <div>页码: 第{{ suggestion.pageNumber }}页</div>
                                <div>段落: 第{{ suggestion.paragraphIndex }}段</div>
                                <div>字符: {{ suggestion.startIndex }}-{{ suggestion.endIndex }}</div>
                                <div>坐标: ({{ suggestion.coordinates.x1 }},{{ suggestion.coordinates.y1 }}) - ({{ suggestion.coordinates.x2 }},{{ suggestion.coordinates.y2 }})</div>
                            </div>
                        </div>
                    </div>
                    <div class="suggestion-actions">
                        <button class="accept-btn" @click.stop="acceptSuggestion(index)">接受</button>
                        <button class="reject-btn" @click.stop="rejectSuggestion(index)">拒绝</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js';

// 类型声明
interface SuggestionItem {
    id: number;
    pageNumber: number;
    coordinates: {
        x1: number;
        y1: number;
        x2: number;
        y2: number;
    };
    paragraphIndex: number;
    startIndex: number;
    endIndex: number;
    originalText: string;
    suggestedText: string;
    color: string;
    status: 'pending' | 'accepted' | 'rejected';
}

// 扩展window对象类型
declare global {
    interface Window {
        DocsAPI: any;
        Api: any;
    }
}
const isComponentReady = ref(false);
const docEditorInstance = ref(null);
const connector = ref(null);
const selectedSuggestion = ref<number | null>(null);

// 示例修改建议列表 - 包含页码和坐标信息
const suggestionList = reactive([
    {
        id: 1,
        pageNumber: 1, // 页码
        coordinates: { // 对角线坐标 (左上角和右下角)
            x1: 100,
            y1: 200,
            x2: 300,
            y2: 250
        },
        paragraphIndex: 2,
        startIndex: 5,
        endIndex: 15,
        originalText: "这是原始文本",
        suggestedText: "这是建议修改后的文本",
        color: "yellow",
        status: "pending" // pending, accepted, rejected
    },
    {
        id: 2,
        pageNumber: 2,
        coordinates: {
            x1: 150,
            y1: 300,
            x2: 400,
            y2: 350
        },
        paragraphIndex: 5,
        startIndex: 10,
        endIndex: 25,
        originalText: "需要修改的内容示例",
        suggestedText: "已优化的内容示例",
        color: "green",
        status: "pending"
    },
    {
        id: 3,
        pageNumber: 3,
        coordinates: {
            x1: 80,
            y1: 150,
            x2: 350,
            y2: 200
        },
        paragraphIndex: 8,
        startIndex: 3,
        endIndex: 20,
        originalText: "这里有一些错误的表述",
        suggestedText: "这里是更准确的表述",
        color: "yellow",
        status: "pending"
    }
]);

const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        // url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        url:'http://172.16.0.116:8090/files/test.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    token: '' as string, // JWT token
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://172.16.0.198:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            console.log('OnlyOffice 应用已准备就绪', event);
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event);
            message.success('文档加载成功');
            
            // 获取文档编辑器实例 - 使用event.target
            if (event && event.target) {
                docEditorInstance.value = event.target;
                
                // 延迟创建connector，确保API完全加载
                setTimeout(() => {
                    try {
                        // 直接使用event.target作为连接器
                        connector.value = event.target;
                        console.log('文档连接器创建成功');
                        console.log('连接器类型:', typeof connector.value);

                        // 检查可用的方法
                        const availableMethods = Object.getOwnPropertyNames(connector.value || {}).filter(
                            prop => typeof (connector.value as any)?.[prop] === 'function'
                        );
                        console.log('连接器可用方法:', availableMethods);

                        // 检查OnlyOffice API状态
                        console.log('window.Api状态:', !!(window as any).Api);
                        console.log('window.DocsAPI状态:', !!window.DocsAPI);

                        // 检查各种可能的API方法
                        const apiMethods = [
                            'callCommand',
                            'executeMethod',
                            'createConnector',
                            'insertText',
                            'getDocumentApi'
                        ];

                        apiMethods.forEach(method => {
                            const available = typeof (connector.value as any)?.[method] === 'function';
                            console.log(`${method}方法:`, available ? '可用' : '不可用');
                        });

                        // 尝试获取文档API的其他方式
                        if (window.DocsAPI && (window.DocsAPI as any).DocEditor) {
                            console.log('尝试通过DocsAPI获取编辑器实例...');
                            const docEditor = document.getElementById('docEditor');
                            if (docEditor && (window.DocsAPI as any).DocEditor.instances) {
                                const editorInstance = (window.DocsAPI as any).DocEditor.instances[docEditor.id];
                                if (editorInstance) {
                                    console.log('通过DocsAPI获取到编辑器实例');
                                    connector.value = editorInstance;
                                }
                            }
                        }

                        // 加载修改建议数据
                        loadSuggestions();

                    } catch (error: any) {
                        console.error('创建文档API失败:', error);
                        message.warning('文档API创建失败，将使用基础功能');

                        // 即使API创建失败，也要加载建议数据用于UI展示
                        connector.value = event.target;
                        loadSuggestions();
                    }
                }, 2000); // 延迟2秒确保OnlyOffice完全初始化
            } else {
                console.error('无法获取文档编辑器实例');
                message.error('无法获取文档编辑器实例');
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
};

// 测试连接函数
const testConnection = () => {
    console.log('=== 连接测试开始 ===');
    console.log('connector状态:', !!connector.value);
    console.log('connector类型:', typeof connector.value);

    if (connector.value) {
        // 获取所有可用方法
        const allMethods = Object.getOwnPropertyNames(connector.value).filter(
            prop => typeof (connector.value as any)[prop] === 'function'
        );
        console.log('connector可用方法:', allMethods);

        // 检查关键API方法
        const keyMethods = [
            'callCommand',
            'executeMethod',
            'createConnector',
            'insertText',
            'getDocumentApi',
            'sendMessage',
            'postMessage'
        ];

        console.log('=== 关键方法检查 ===');
        keyMethods.forEach(method => {
            const available = typeof (connector.value as any)?.[method] === 'function';
            console.log(`${method}:`, available ? '✅ 可用' : '❌ 不可用');
        });

        // 检查全局对象
        console.log('=== 全局对象检查 ===');
        console.log('window.Api:', !!(window as any).Api ? '✅ 可用' : '❌ 不可用');
        console.log('window.DocsAPI:', !!window.DocsAPI ? '✅ 可用' : '❌ 不可用');
        console.log('window.Asc:', !!(window as any).Asc ? '✅ 可用' : '❌ 不可用');

        // 检查DOM元素
        console.log('=== DOM元素检查 ===');
        const docEditor = document.getElementById('docEditor');
        console.log('docEditor元素:', !!docEditor ? '✅ 存在' : '❌ 不存在');

        const editorFrame = document.querySelector('#docEditor iframe');
        console.log('编辑器iframe:', !!editorFrame ? '✅ 存在' : '❌ 不存在');

        // 尝试不同的API调用方式
        console.log('=== API调用测试 ===');

        // 测试executeMethod
        if (typeof (connector.value as any).executeMethod === 'function') {
            console.log('测试executeMethod...');
            try {
                (connector.value as any).executeMethod("GetVersion", [], (result: any) => {
                    console.log('GetVersion结果:', result);
                });

                (connector.value as any).executeMethod("GetCurrentWord", [], (result: any) => {
                    console.log('GetCurrentWord结果:', result);
                });
            } catch (error) {
                console.error('executeMethod测试失败:', error);
            }
        }

        // 测试postMessage
        try {
            const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
            if (editorFrame && editorFrame.contentWindow) {
                editorFrame.contentWindow.postMessage({
                    type: 'test',
                    data: { message: '连接测试' }
                }, '*');
                console.log('postMessage测试发送成功');
            }
        } catch (error) {
            console.error('postMessage测试失败:', error);
        }

        message.success('连接测试完成，请查看控制台详细信息');
    } else {
        console.warn('connector未初始化');
        message.warning('连接器未初始化');
    }

    console.log('=== 连接测试结束 ===');
};

// 模拟高亮功能（用于演示）
const simulateHighlight = () => {
    if (suggestionList.length === 0) {
        message.warning('没有可用的建议');
        return;
    }

    // 随机选择一个建议进行模拟高亮
    const randomIndex = Math.floor(Math.random() * suggestionList.length);
    const suggestion = suggestionList[randomIndex];

    console.log('=== 模拟高亮演示 ===');
    console.log('选中建议:', suggestion);
    console.log('页码:', suggestion.pageNumber);
    console.log('坐标:', suggestion.coordinates);
    console.log('原文:', suggestion.originalText);
    console.log('建议:', suggestion.suggestedText);

    // 更新选中状态
    selectedSuggestion.value = randomIndex;

    // 显示模拟效果
    message.info(`模拟高亮: 页码${suggestion.pageNumber}, 坐标(${suggestion.coordinates.x1},${suggestion.coordinates.y1})-(${suggestion.coordinates.x2},${suggestion.coordinates.y2})`);

    // 模拟滚动效果（延迟显示）
    setTimeout(() => {
        message.success(`已定位到建议 #${randomIndex + 1}: "${suggestion.originalText}" → "${suggestion.suggestedText}"`);
    }, 1000);

    console.log('=== 模拟高亮完成 ===');
};

// 通用的文档操作函数
const executeDocumentCommand = (commandName: string, params: any = {}, callback?: (result: any) => void) => {
    if (!connector.value) {
        console.error('文档连接器未初始化');
        return;
    }

    try {
        // 优先使用executeMethod API
        if (typeof (connector.value as any).executeMethod === 'function') {
            (connector.value as any).executeMethod(commandName, params, callback);
        } else if (typeof (connector.value as any).callCommand === 'function') {
            // 回退到callCommand API
            (connector.value as any).callCommand(function() {
                try {
                    // 这里可以根据commandName执行不同的操作
                    console.log('执行文档命令:', commandName, params);
                    if (callback) callback({ success: true });
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                    if (callback) callback({ success: false, error: e });
                }
            });
        } else {
            throw new Error('找不到可用的文档API方法');
        }
    } catch (error) {
        console.error('执行文档命令失败:', error);
        if (callback) callback({ success: false, error });
    }
};

// 选择建议，定位到文档对应位置并高亮
const selectSuggestion = (index: number) => {
    selectedSuggestion.value = index;
    const suggestion = suggestionList[index];

    console.log('选择建议:', suggestion);
    console.log('页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

    // 显示选择状态
    message.info(`已选择建议 #${index + 1}: 页码 ${suggestion.pageNumber}, 坐标 (${suggestion.coordinates.x1},${suggestion.coordinates.y1}) - (${suggestion.coordinates.x2},${suggestion.coordinates.y2})`);

    // 尝试多种方式进行文档操作
    if (connector.value) {
        console.log('尝试文档操作...');

        // 方法1: 尝试使用executeMethod
        if (typeof (connector.value as any).executeMethod === 'function') {
            console.log('尝试使用executeMethod...');
            try {
                // 尝试获取当前选择
                (connector.value as any).executeMethod("GetCurrentWord", [], (result: any) => {
                    console.log('当前单词:', result);
                });

                // 尝试插入文本（测试API是否工作）
                (connector.value as any).executeMethod("PasteText", [`[建议#${index + 1}已选择]`], (result: any) => {
                    console.log('插入文本结果:', result);
                });
            } catch (error) {
                console.error('executeMethod调用失败:', error);
            }
        }

        // 方法2: 尝试使用callCommand（即使显示不可用也试试）
        else if (typeof (connector.value as any).callCommand === 'function') {
            console.log('尝试使用callCommand...');
            // 之前的callCommand代码保持不变
        }

        // 方法3: 尝试直接操作DOM或使用其他API
        else {
            console.log('尝试其他方法...');

            // 尝试通过postMessage与编辑器通信
            try {
                const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
                if (editorFrame && editorFrame.contentWindow) {
                    editorFrame.contentWindow.postMessage({
                        type: 'selectSuggestion',
                        data: suggestion
                    }, '*');
                    console.log('已发送postMessage到编辑器');
                }
            } catch (error) {
                console.error('postMessage发送失败:', error);
            }

            // 模拟高亮效果（在UI上显示）
            console.log(`模拟高亮: 页码${suggestion.pageNumber}, 段落${suggestion.paragraphIndex}, 位置${suggestion.startIndex}-${suggestion.endIndex}`);
        }
    } else {
        console.warn('文档连接器未初始化，仅更新UI状态');
    }

    // 无论API是否可用，都更新UI状态
    console.log('已选择建议', index, '- UI状态已更新');
};

// 接受建议，修改文档内容
const acceptSuggestion = (index: number) => {
    const suggestion = suggestionList[index];

    console.log('接受建议:', suggestion);
    console.log('页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

    // 尝试多种方式进行文档修改
    if (connector.value) {
        console.log('尝试修改文档...');

        // 方法1: 尝试使用executeMethod插入文本
        if (typeof (connector.value as any).executeMethod === 'function') {
            console.log('尝试使用executeMethod插入文本...');
            try {
                // 插入建议文本到当前位置
                (connector.value as any).executeMethod("PasteText", [suggestion.suggestedText], (result: any) => {
                    console.log('文本插入结果:', result);
                    message.success(`已插入建议文本: "${suggestion.suggestedText}"`);
                });
            } catch (error) {
                console.error('executeMethod插入文本失败:', error);
            }
        }

        // 方法2: 尝试使用其他可能的API方法
        else {
            console.log('尝试其他文档修改方法...');

            // 检查是否有insertText方法
            if (typeof (connector.value as any).insertText === 'function') {
                try {
                    (connector.value as any).insertText(suggestion.suggestedText);
                    console.log('使用insertText插入文本成功');
                } catch (error) {
                    console.error('insertText失败:', error);
                }
            }

            // 尝试通过postMessage发送修改指令
            try {
                const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
                if (editorFrame && editorFrame.contentWindow) {
                    editorFrame.contentWindow.postMessage({
                        type: 'acceptSuggestion',
                        data: {
                            suggestion,
                            action: 'replace',
                            originalText: suggestion.originalText,
                            newText: suggestion.suggestedText
                        }
                    }, '*');
                    console.log('已发送文档修改指令到编辑器');
                }
            } catch (error) {
                console.error('postMessage发送修改指令失败:', error);
            }
        }
    } else {
        console.warn('文档连接器未初始化，仅更新UI状态');
    }

    console.log('已接受修改建议', index);

    // 更新建议状态为已接受
    suggestion.status = 'accepted';

    // 从列表中移除已接受的建议
    suggestionList.splice(index, 1);
    selectedSuggestion.value = null;

    // 显示成功消息，包含详细信息
    message.success(`已接受建议 #${index + 1}: "${suggestion.originalText}" → "${suggestion.suggestedText}"`);
};

// 拒绝建议，从列表中移除
const rejectSuggestion = (index: number) => {
    const suggestion = suggestionList[index];

    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }

    try {
        console.log('拒绝建议:', suggestion);

        // 使用callCommand API移除高亮
        if (typeof (connector.value as any).callCommand === 'function') {
            (connector.value as any).callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = (window as any).Api.GetDocument();

                    // 获取指定段落
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }

                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 获取文本范围并移除高亮
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);
                        oRange.SetHighlight("none");

                        console.log('已移除高亮，坐标区域:', suggestion.coordinates);
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        }

        console.log('已拒绝修改建议', index);

        // 更新建议状态为已拒绝
        suggestion.status = 'rejected';

        // 从列表中移除已拒绝的建议
        suggestionList.splice(index, 1);
        selectedSuggestion.value = null;
        message.success('已拒绝修改建议');
    } catch (error: any) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + (error?.message || '未知错误'));
    }
};

// 从API加载修改建议数据
const loadSuggestions = async () => {
    try {
        // 这里可以替换为实际的API调用
        // const response = await fetch('/api/suggestions');
        // const data = await response.json();
        // suggestionList.splice(0, suggestionList.length, ...data);

        console.log('加载修改建议数据');
        console.log('建议列表:', suggestionList);

        // 简化初始化，不立即添加高亮，等用户点击时再处理
        message.success(`已加载 ${suggestionList.length} 条修改建议`);

    } catch (error) {
        console.error('加载修改建议数据失败:', error);
        message.error('加载修改建议数据失败');
    }
};

onMounted(async () => {
    // 生成JWT令牌
    setupJWT();
    
    nextTick(() => {
        isComponentReady.value = true;
    });
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.debug-info {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f0f0f0;
    border-radius: 4px;
}

.test-btn {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 8px;
}

.test-btn:hover {
    background-color: #40a9ff;
}

.status-info {
    font-size: 12px;
    color: #666;
}

.status-info > div {
    margin-bottom: 4px;
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

