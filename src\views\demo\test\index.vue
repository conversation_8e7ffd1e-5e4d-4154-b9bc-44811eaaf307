<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://172.16.0.198:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel">
            <h3>修改建议列表</h3>
            <div class="suggestion-list">
                <div 
                    v-for="(suggestion, index) in suggestionList" 
                    :key="index" 
                    class="suggestion-item"
                    :class="{ 'active': selectedSuggestion === index }"
                    @click="selectSuggestion(index)"
                >
                    <div class="suggestion-content">
                        <div class="suggestion-title">
                            修改建议 #{{ index + 1 }}
                            <span class="suggestion-status" :class="suggestion.status">
                                {{ suggestion.status === 'pending' ? '待处理' : suggestion.status === 'accepted' ? '已接受' : '已拒绝' }}
                            </span>
                        </div>
                        <div class="suggestion-text">
                            <div class="original-text">原文: {{ suggestion.originalText }}</div>
                            <div class="suggested-text">建议: {{ suggestion.suggestedText }}</div>
                            <div class="location">
                                <div>页码: 第{{ suggestion.pageNumber }}页</div>
                                <div>段落: 第{{ suggestion.paragraphIndex }}段</div>
                                <div>字符: {{ suggestion.startIndex }}-{{ suggestion.endIndex }}</div>
                                <div>坐标: ({{ suggestion.coordinates.x1 }},{{ suggestion.coordinates.y1 }}) - ({{ suggestion.coordinates.x2 }},{{ suggestion.coordinates.y2 }})</div>
                            </div>
                        </div>
                    </div>
                    <div class="suggestion-actions">
                        <button class="accept-btn" @click.stop="acceptSuggestion(index)">接受</button>
                        <button class="reject-btn" @click.stop="rejectSuggestion(index)">拒绝</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js';

// 类型声明
interface SuggestionItem {
    id: number;
    pageNumber: number;
    coordinates: {
        x1: number;
        y1: number;
        x2: number;
        y2: number;
    };
    paragraphIndex: number;
    startIndex: number;
    endIndex: number;
    originalText: string;
    suggestedText: string;
    color: string;
    status: 'pending' | 'accepted' | 'rejected';
}

// 扩展window对象类型
declare global {
    interface Window {
        DocsAPI: any;
        Api: any;
    }
}
const isComponentReady = ref(false);
const docEditorInstance = ref(null);
const connector = ref(null);
const selectedSuggestion = ref<number | null>(null);

// 示例修改建议列表 - 包含页码和坐标信息
const suggestionList = reactive([
    {
        id: 1,
        pageNumber: 1, // 页码
        coordinates: { // 对角线坐标 (左上角和右下角)
            x1: 100,
            y1: 200,
            x2: 300,
            y2: 250
        },
        paragraphIndex: 2,
        startIndex: 5,
        endIndex: 15,
        originalText: "这是原始文本",
        suggestedText: "这是建议修改后的文本",
        color: "yellow",
        status: "pending" // pending, accepted, rejected
    },
    {
        id: 2,
        pageNumber: 2,
        coordinates: {
            x1: 150,
            y1: 300,
            x2: 400,
            y2: 350
        },
        paragraphIndex: 5,
        startIndex: 10,
        endIndex: 25,
        originalText: "需要修改的内容示例",
        suggestedText: "已优化的内容示例",
        color: "green",
        status: "pending"
    },
    {
        id: 3,
        pageNumber: 3,
        coordinates: {
            x1: 80,
            y1: 150,
            x2: 350,
            y2: 200
        },
        paragraphIndex: 8,
        startIndex: 3,
        endIndex: 20,
        originalText: "这里有一些错误的表述",
        suggestedText: "这里是更准确的表述",
        color: "yellow",
        status: "pending"
    }
]);

const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://172.16.0.198:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            console.log('OnlyOffice 应用已准备就绪', event);
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event);
            message.success('文档加载成功');
            
            // 获取文档编辑器实例 - 使用event.target
            if (event && event.target) {
                docEditorInstance.value = event.target;
                
                // 创建connector用于执行文档命令
                try {
                    // 直接使用event.target作为API接口
                    connector.value = event.target;
                    
                    // 检查connector是否有必要的方法
                    if (!connector.value || typeof connector.value.executeMethod !== 'function') {
                        // 尝试从window对象获取DocsAPI
                        if (window.DocsAPI && typeof window.DocsAPI.DocEditor !== 'undefined') {
                            const docEditor = document.getElementById('docEditor');
                            if (docEditor) {
                                // 尝试获取编辑器实例
                                connector.value = window.DocsAPI.DocEditor.instances[docEditor.getAttribute('id')];
                            }
                        }
                        
                        if (!connector.value || typeof connector.value.executeMethod !== 'function') {
                            throw new Error('无法获取有效的文档API接口');
                        }
                    }
                    
                    console.log('文档连接器创建成功', connector.value);
                    
                    // 测试API是否可用
                    const apiVersion = connector.value.executeMethod ? 
                        connector.value.executeMethod('GetVersion') : 
                        '未知版本';
                    console.log('OnlyOffice API版本:', apiVersion);
                    
                    // 加载修改建议数据
                    loadSuggestions();
                } catch (error) {
                    console.error('创建文档API失败:', error);
                    message.error('创建文档API失败: ' + error.message);
                    
                    // 输出更多调试信息
                    console.log('event对象:', event);
                    if (event && event.target) {
                        console.log('event.target类型:', typeof event.target);
                        console.log('event.target属性:', Object.keys(event.target));
                        console.log('event.target方法:', Object.getOwnPropertyNames(event.target).filter(
                            prop => typeof event.target[prop] === 'function'
                        ));
                    }
                    
                    // 检查全局DocsAPI对象
                    if (window.DocsAPI) {
                        console.log('DocsAPI可用:', window.DocsAPI);
                    } else {
                        console.log('DocsAPI不可用');
                    }
                }
            } else {
                console.error('无法获取文档编辑器实例');
                message.error('无法获取文档编辑器实例');
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
};

// 通用的文档操作函数
const executeDocumentCommand = (commandName: string, params: any = {}, callback?: (result: any) => void) => {
    if (!connector.value) {
        console.error('文档连接器未初始化');
        return;
    }

    try {
        // 优先使用executeMethod API
        if (typeof (connector.value as any).executeMethod === 'function') {
            (connector.value as any).executeMethod(commandName, params, callback);
        } else if (typeof (connector.value as any).callCommand === 'function') {
            // 回退到callCommand API
            (connector.value as any).callCommand(function() {
                try {
                    // 这里可以根据commandName执行不同的操作
                    console.log('执行文档命令:', commandName, params);
                    if (callback) callback({ success: true });
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                    if (callback) callback({ success: false, error: e });
                }
            });
        } else {
            throw new Error('找不到可用的文档API方法');
        }
    } catch (error) {
        console.error('执行文档命令失败:', error);
        if (callback) callback({ success: false, error });
    }
};

// 选择建议，定位到文档对应位置并高亮
const selectSuggestion = (index: number) => {
    selectedSuggestion.value = index;
    const suggestion = suggestionList[index];

    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }

    try {
        console.log('选择建议:', suggestion);
        console.log('页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

        // 使用callCommand API进行文档操作
        if (typeof (connector.value as any).callCommand === 'function') {
            (connector.value as any).callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = (window as any).Api.GetDocument();

                    console.log('开始定位到页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

                    // 1. 根据段落信息定位文本
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }

                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 获取文本范围
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);

                        // 2. 高亮显示选中区域
                        oRange.SetHighlight(suggestion.color || "yellow");

                        // 3. 选中文本并滚动到可见区域
                        oRange.Select();

                        console.log('已定位并高亮显示，页码:', suggestion.pageNumber, '坐标区域:', suggestion.coordinates);
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        } else {
            console.warn('找不到可用的文档API方法');
        }
        
        console.log('已选择并高亮显示建议', index);
    } catch (error: any) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + (error?.message || '未知错误'));
    }
};

// 接受建议，修改文档内容
const acceptSuggestion = (index: number) => {
    const suggestion = suggestionList[index];

    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }

    try {
        console.log('接受建议:', suggestion);
        console.log('页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

        // 使用callCommand API进行文档修改
        if (typeof (connector.value as any).callCommand === 'function') {
            (connector.value as any).callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = (window as any).Api.GetDocument();

                    console.log('开始替换文本，页码:', suggestion.pageNumber, '坐标:', suggestion.coordinates);

                    // 1. 获取指定段落
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }

                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 2. 获取要替换的文本范围
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);

                        // 开始批量操作以提高性能
                        if ((window as any).Api.BeginTransaction) {
                            (window as any).Api.BeginTransaction();
                        }

                        try {
                            // 3. 删除原文本并添加建议文本
                            oRange.Delete();

                            // 4. 在删除位置插入新文本
                            var oNewRange = element.GetRange(suggestion.startIndex, suggestion.startIndex);
                            oNewRange.AddText(suggestion.suggestedText);

                            // 5. 移除高亮（如果有的话）
                            var oUpdatedRange = element.GetRange(suggestion.startIndex, suggestion.startIndex + suggestion.suggestedText.length);
                            oUpdatedRange.SetHighlight("none");

                            console.log('文本替换完成，页码:', suggestion.pageNumber, '坐标区域:', suggestion.coordinates);
                        } finally {
                            // 结束批量操作
                            if ((window as any).Api.EndTransaction) {
                                (window as any).Api.EndTransaction();
                            }
                        }
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        } else {
            console.warn('找不到可用的文档API方法');
        }
        
        console.log('已接受修改建议', index);

        // 更新建议状态为已接受
        suggestion.status = 'accepted';

        // 从列表中移除已接受的建议
        suggestionList.splice(index, 1);
        selectedSuggestion.value = null;
        message.success('已接受修改建议');
    } catch (error: any) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + (error?.message || '未知错误'));
    }
};

// 拒绝建议，从列表中移除
const rejectSuggestion = (index: number) => {
    const suggestion = suggestionList[index];

    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }

    try {
        console.log('拒绝建议:', suggestion);

        // 使用callCommand API移除高亮
        if (typeof (connector.value as any).callCommand === 'function') {
            (connector.value as any).callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = (window as any).Api.GetDocument();

                    // 获取指定段落
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }

                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 获取文本范围并移除高亮
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);
                        oRange.SetHighlight("none");

                        console.log('已移除高亮，坐标区域:', suggestion.coordinates);
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        }

        console.log('已拒绝修改建议', index);

        // 更新建议状态为已拒绝
        suggestion.status = 'rejected';

        // 从列表中移除已拒绝的建议
        suggestionList.splice(index, 1);
        selectedSuggestion.value = null;
        message.success('已拒绝修改建议');
    } catch (error: any) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + (error?.message || '未知错误'));
    }
};

// 从API加载修改建议数据
const loadSuggestions = async () => {
    try {
        // 这里可以替换为实际的API调用
        // const response = await fetch('/api/suggestions');
        // const data = await response.json();
        // suggestionList.splice(0, suggestionList.length, ...data);
        
        // 目前使用模拟数据
        console.log('加载修改建议数据');
        
        // 如果connector已初始化，为每个建议添加高亮
        if (connector.value) {
            for (let i = 0; i < suggestionList.length; i++) {
                const suggestion = suggestionList[i];

                try {
                    // 使用callCommand API为建议添加初始高亮
                    if (typeof (connector.value as any).callCommand === 'function') {
                        (connector.value as any).callCommand(function() {
                            try {
                                // 获取文档对象
                                var oDocument = (window as any).Api.GetDocument();

                                // 获取指定段落
                                var element = oDocument.GetElement(suggestion.paragraphIndex);
                                if (!element) {
                                    console.error('找不到指定段落');
                                    return;
                                }

                                // 如果是段落类型
                                if (element.GetClassType() === "paragraph") {
                                    // 获取文本范围
                                    var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);

                                    // 高亮显示
                                    oRange.SetHighlight(suggestion.color || "yellow");

                                    console.log('已为建议添加高亮，坐标:', suggestion.coordinates);
                                }
                            } catch (e) {
                                console.error('执行callCommand时出错:', e);
                            }
                        });
                    } else {
                        console.warn('找不到可用的文档API方法');
                    }
                } catch (err) {
                    console.warn('无法为建议添加高亮:', err);
                }
            }
        }
    } catch (error) {
        console.error('加载修改建议数据失败:', error);
        message.error('加载修改建议数据失败');
    }
};

onMounted(async () => {
    // 生成JWT令牌
    setupJWT();
    
    nextTick(() => {
        isComponentReady.value = true;
    });
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

