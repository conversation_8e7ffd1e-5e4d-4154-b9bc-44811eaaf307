<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://172.16.0.198:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel">
            <h3>修改建议列表</h3>
            <div class="suggestion-list">
                <div 
                    v-for="(suggestion, index) in suggestionList" 
                    :key="index" 
                    class="suggestion-item"
                    :class="{ 'active': selectedSuggestion === index }"
                    @click="selectSuggestion(index)"
                >
                    <div class="suggestion-content">
                        <div class="suggestion-title">修改建议 #{{ index + 1 }}</div>
                        <div class="suggestion-text">
                            <div class="original-text">原文: {{ suggestion.originalText }}</div>
                            <div class="suggested-text">建议: {{ suggestion.suggestedText }}</div>
                            <div class="location">位置: 第{{ suggestion.paragraphIndex }}段, 第{{ suggestion.startIndex }}-{{ suggestion.endIndex }}字符</div>
                        </div>
                    </div>
                    <div class="suggestion-actions">
                        <button class="accept-btn" @click.stop="acceptSuggestion(index)">接受</button>
                        <button class="reject-btn" @click.stop="rejectSuggestion(index)">拒绝</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue'; 
import CryptoJS from 'crypto-js';
const isComponentReady = ref(false);
const docEditorInstance = ref(null);
const connector = ref(null);
const selectedSuggestion = ref(null);

// 示例修改建议列表
const suggestionList = reactive([
    {
        paragraphIndex: 2,
        startIndex: 5,
        endIndex: 15,
        originalText: "这是原始文本",
        suggestedText: "这是建议修改后的文本",
        color: "yellow"
    },
    {
        paragraphIndex: 5,
        startIndex: 10,
        endIndex: 25,
        originalText: "需要修改的内容示例",
        suggestedText: "已优化的内容示例",
        color: "green"
    },
    {
        paragraphIndex: 8,
        startIndex: 3,
        endIndex: 20,
        originalText: "这里有一些错误的表述",
        suggestedText: "这里是更准确的表述",
        color: "yellow"
    }
]);

const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://172.16.0.198:18000/callback"
    },
    events: {
        onAppReady: (event) => {
            console.log('OnlyOffice 应用已准备就绪', event); 
        },
        onDocumentReady: (event) => {
            console.log('文档加载成功', event);
            message.success('文档加载成功');
            
            // 获取文档编辑器实例 - 使用event.target
            if (event && event.target) {
                docEditorInstance.value = event.target;
                
                // 创建connector用于执行文档命令
                try {
                    // 直接使用event.target作为API接口
                    connector.value = event.target;
                    
                    // 检查connector是否有必要的方法
                    if (!connector.value || typeof connector.value.executeMethod !== 'function') {
                        // 尝试从window对象获取DocsAPI
                        if (window.DocsAPI && typeof window.DocsAPI.DocEditor !== 'undefined') {
                            const docEditor = document.getElementById('docEditor');
                            if (docEditor) {
                                // 尝试获取编辑器实例
                                connector.value = window.DocsAPI.DocEditor.instances[docEditor.getAttribute('id')];
                            }
                        }
                        
                        if (!connector.value || typeof connector.value.executeMethod !== 'function') {
                            throw new Error('无法获取有效的文档API接口');
                        }
                    }
                    
                    console.log('文档连接器创建成功', connector.value);
                    
                    // 测试API是否可用
                    const apiVersion = connector.value.executeMethod ? 
                        connector.value.executeMethod('GetVersion') : 
                        '未知版本';
                    console.log('OnlyOffice API版本:', apiVersion);
                    
                    // 加载修改建议数据
                    loadSuggestions();
                } catch (error) {
                    console.error('创建文档API失败:', error);
                    message.error('创建文档API失败: ' + error.message);
                    
                    // 输出更多调试信息
                    console.log('event对象:', event);
                    if (event && event.target) {
                        console.log('event.target类型:', typeof event.target);
                        console.log('event.target属性:', Object.keys(event.target));
                        console.log('event.target方法:', Object.getOwnPropertyNames(event.target).filter(
                            prop => typeof event.target[prop] === 'function'
                        ));
                    }
                    
                    // 检查全局DocsAPI对象
                    if (window.DocsAPI) {
                        console.log('DocsAPI可用:', window.DocsAPI);
                    } else {
                        console.log('DocsAPI不可用');
                    }
                }
            } else {
                console.error('无法获取文档编辑器实例');
                message.error('无法获取文档编辑器实例');
            }
        },
        onError: (event) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event) => {
            console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
};
// 选择建议，定位到文档对应位置并高亮
const selectSuggestion = (index) => {
    selectedSuggestion.value = index;
    const suggestion = suggestionList[index];
    
    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }
    
    try {
        // 检查API调用方式
        if (typeof connector.value.executeMethod === 'function') {
            // 使用executeMethod API
            // 选择文本范围
            connector.value.executeMethod("SetSelectionRange", {
                start: {
                    paragraph: suggestion.paragraphIndex,
                    position: suggestion.startIndex
                },
                end: {
                    paragraph: suggestion.paragraphIndex,
                    position: suggestion.endIndex
                }
            });
            
            // 高亮显示选中文本
            connector.value.executeMethod("SetTextHighlight", {
                color: suggestion.color || "yellow"
            });
            
            // 滚动到可见区域
            connector.value.executeMethod("ScrollToPosition", {
                paragraph: suggestion.paragraphIndex,
                position: suggestion.startIndex
            });
        } else if (typeof connector.value.callCommand === 'function') {
            // 使用callCommand API
            connector.value.callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = Api.GetDocument();
                    
                    // 获取指定段落
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }
                    
                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 获取文本范围
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);
                        
                        // 高亮显示
                        oRange.SetHighlight(suggestion.color || "yellow");
                        
                        // 滚动到可见区域并选中
                        oRange.Select();
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        } else {
            throw new Error('找不到可用的文档API方法');
        }
        
        console.log('已选择并高亮显示建议', index);
    } catch (error) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + error.message);
    }
};

// 接受建议，修改文档内容
const acceptSuggestion = (index) => {
    const suggestion = suggestionList[index];
    
    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }
    
    try {
        // 检查API调用方式
        if (typeof connector.value.executeMethod === 'function') {
            // 使用executeMethod API
            // 首先选中要替换的文本
            connector.value.executeMethod("SetSelectionRange", {
                start: {
                    paragraph: suggestion.paragraphIndex,
                    position: suggestion.startIndex
                },
                end: {
                    paragraph: suggestion.paragraphIndex,
                    position: suggestion.endIndex
                }
            });
            
            // 替换选中的文本
            connector.value.executeMethod("ReplaceText", {
                text: suggestion.suggestedText
            });
            
            // 移除高亮
            connector.value.executeMethod("SetTextHighlight", {
                color: "none"
            });
            
            // 设置光标位置到修改后的文本末尾
            connector.value.executeMethod("SetPosition", {
                paragraph: suggestion.paragraphIndex,
                position: suggestion.startIndex + suggestion.suggestedText.length
            });
        } else if (typeof connector.value.callCommand === 'function') {
            // 使用callCommand API
            connector.value.callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = Api.GetDocument();
                    
                    // 获取指定段落
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }
                    
                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 获取文本范围
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);
                        
                        // 开始批量操作以提高性能
                        Api.BeginTransaction();
                        
                        try {
                            // 删除原文本并添加建议文本
                            oRange.Delete();
                            oRange.AddText(suggestion.suggestedText);
                            
                            // 移除高亮
                            oRange = element.GetRange(suggestion.startIndex, suggestion.startIndex + suggestion.suggestedText.length);
                            oRange.SetHighlight("none");
                        } finally {
                            // 结束批量操作
                            Api.EndTransaction();
                        }
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        } else {
            throw new Error('找不到可用的文档API方法');
        }
        
        console.log('已接受修改建议', index);
        
        // 从列表中移除已接受的建议
        suggestionList.splice(index, 1);
        selectedSuggestion.value = null;
        message.success('已接受修改建议');
    } catch (error) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + error.message);
    }
};

// 拒绝建议，从列表中移除
const rejectSuggestion = (index) => {
    const suggestion = suggestionList[index];
    
    if (!connector.value) {
        message.error('文档连接器未初始化');
        return;
    }
    
    try {
        // 检查API调用方式
        if (typeof connector.value.executeMethod === 'function') {
            // 使用executeMethod API
            // 首先选中要处理的文本
            connector.value.executeMethod("SetSelectionRange", {
                start: {
                    paragraph: suggestion.paragraphIndex,
                    position: suggestion.startIndex
                },
                end: {
                    paragraph: suggestion.paragraphIndex,
                    position: suggestion.endIndex
                }
            });
            
            // 移除高亮
            connector.value.executeMethod("SetTextHighlight", {
                color: "none"
            });
            
            // 设置光标位置到原文本末尾
            connector.value.executeMethod("SetPosition", {
                paragraph: suggestion.paragraphIndex,
                position: suggestion.endIndex
            });
        } else if (typeof connector.value.callCommand === 'function') {
            // 使用callCommand API
            connector.value.callCommand(function() {
                try {
                    // 获取文档对象
                    var oDocument = Api.GetDocument();
                    
                    // 获取指定段落
                    var element = oDocument.GetElement(suggestion.paragraphIndex);
                    if (!element) {
                        console.error('找不到指定段落');
                        return;
                    }
                    
                    // 如果是段落类型
                    if (element.GetClassType() === "paragraph") {
                        // 获取文本范围
                        var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);
                        
                        // 移除高亮
                        oRange.SetHighlight("none");
                    }
                } catch (e) {
                    console.error('执行callCommand时出错:', e);
                }
            });
        } else {
            throw new Error('找不到可用的文档API方法');
        }
        
        console.log('已拒绝修改建议', index);
        
        // 从列表中移除已拒绝的建议
        suggestionList.splice(index, 1);
        selectedSuggestion.value = null;
        message.success('已拒绝修改建议');
    } catch (error) {
        console.error('执行文档命令时出错:', error);
        message.error('执行文档命令失败: ' + error.message);
    }
};

// 从API加载修改建议数据
const loadSuggestions = async () => {
    try {
        // 这里可以替换为实际的API调用
        // const response = await fetch('/api/suggestions');
        // const data = await response.json();
        // suggestionList.splice(0, suggestionList.length, ...data);
        
        // 目前使用模拟数据
        console.log('加载修改建议数据');
        
        // 如果connector已初始化，为每个建议添加高亮
        if (connector.value) {
            for (let i = 0; i < suggestionList.length; i++) {
                const suggestion = suggestionList[i];
                
                try {
                    if (typeof connector.value.executeMethod === 'function') {
                        // 使用executeMethod API
                        // 选中文本
                        connector.value.executeMethod("SetSelectionRange", {
                            start: {
                                paragraph: suggestion.paragraphIndex,
                                position: suggestion.startIndex
                            },
                            end: {
                                paragraph: suggestion.paragraphIndex,
                                position: suggestion.endIndex
                            }
                        });
                        
                        // 高亮显示
                        connector.value.executeMethod("SetTextHighlight", {
                            color: suggestion.color || "yellow"
                        });
                    } else if (typeof connector.value.callCommand === 'function') {
                        // 使用callCommand API
                        connector.value.callCommand(function() {
                            try {
                                // 获取文档对象
                                var oDocument = Api.GetDocument();
                                
                                // 获取指定段落
                                var element = oDocument.GetElement(suggestion.paragraphIndex);
                                if (!element) {
                                    console.error('找不到指定段落');
                                    return;
                                }
                                
                                // 如果是段落类型
                                if (element.GetClassType() === "paragraph") {
                                    // 获取文本范围
                                    var oRange = element.GetRange(suggestion.startIndex, suggestion.endIndex);
                                    
                                    // 高亮显示
                                    oRange.SetHighlight(suggestion.color || "yellow");
                                }
                            } catch (e) {
                                console.error('执行callCommand时出错:', e);
                            }
                        });
                    } else {
                        console.warn('找不到可用的文档API方法');
                    }
                } catch (err) {
                    console.warn('无法为建议添加高亮:', err);
                }
            }
            
            // 重置选择
            if (typeof connector.value.executeMethod === 'function') {
                connector.value.executeMethod("ResetSelection");
            }
        }
    } catch (error) {
        console.error('加载修改建议数据失败:', error);
        message.error('加载修改建议数据失败');
    }
};

onMounted(async () => {
    // 生成JWT令牌
    setupJWT();
    
    nextTick(() => {
        isComponentReady.value = true;
    });
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

